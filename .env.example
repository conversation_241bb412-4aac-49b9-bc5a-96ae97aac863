APP_NAME=skeleton
APP_ENV=test

DB_DRIVER=mysql
DB_HOST=**************
DB_PORT=33006
DB_DATABASE=yshk
DB_USERNAME=root
DB_PASSWORD=123456

DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=

REDIS_HOST=**************
REDIS_AUTH=123456
REDIS_PORT=6379
REDIS_DB=0

# rabbitMQ 配置
RABBITMQ_HOST=**************
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=123456

# 定时任务是否开启
CRONTAB = true

# HTTP服务端口
HTTP_HOST=0.0.0.0
HTTP_PORT=9501

# 航空数据RPC服务配置
AIR_HOST=**************
AIR_PORT=8003

# 黑屏RPC服务配置
ETERM_HOST=**************
ETERM_PORT=3001










