APP_NAME=skeleton
APP_ENV=production

DB_DRIVER=mysql
DB_HOST=*************
DB_PORT=3306
DB_DATABASE=flight
DB_USERNAME=flight
DB_PASSWORD=xayxb9HN4p7dfW5S#
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=

REDIS_HOST=*************
REDIS_AUTH=X5NfmSHPvUPkRtRy
REDIS_PORT=6379
REDIS_DB=1

# rabbitMQ 配置
RABBITMQ_HOST=*************
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=FYzad4UfFHM44DmS

# 定时任务是否开启
CRONTAB = false

# HTTP服务端口
HTTP_HOST=0.0.0.0
HTTP_PORT=9501

# 航空数据RPC服务配置
AIR_HOST=127.0.0.1
AIR_PORT=9504

# 查询报价RPC服务配置
FLIGHT_HOST=*************
FLIGHT_PORT=9506

# 黑屏RPC服务配置
ETERM_HOST=127.0.0.1
ETERM_PORT=9504


