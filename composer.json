{"name": "hyperf/hyperf-skeleton", "type": "project", "keywords": ["php", "swoole", "framework", "hyperf", "microservice", "middleware"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "license": "Apache-2.0", "require": {"php": ">=7.3", "ext-simplexml": "*", "alibabacloud/aliyun-log-php-sdk": "^0.6.3", "aliyuncs/oss-sdk-php": "~2.4", "andphp/jsonrpc": "^2.0", "aws/aws-sdk-php": "^3.263", "hyperf/amqp": "2.2.*", "hyperf/async-queue": "2.2.*", "hyperf/cache": "2.2.*", "hyperf/command": "2.2.*", "hyperf/config": "2.2.*", "hyperf/constants": "2.2.*", "hyperf/crontab": "^2.2", "hyperf/database": "2.2.*", "hyperf/db-connection": "2.2.*", "hyperf/di": "2.2.*", "hyperf/elasticsearch": "2.2.*", "hyperf/event": "2.2.*", "hyperf/filesystem": "^2.2", "hyperf/flysystem-oss": "^1.2", "hyperf/framework": "2.2.*", "hyperf/guzzle": "2.2.*", "hyperf/http-server": "2.2.*", "hyperf/json-rpc": "2.2.*", "hyperf/logger": "2.2.*", "hyperf/memory": "2.2.*", "hyperf/model-cache": "2.2.*", "hyperf/paginator": "^2.2", "hyperf/process": "2.2.*", "hyperf/redis": "2.2.*", "hyperf/retry": "^2.2", "hyperf/rpc": "2.2.*", "hyperf/rpc-client": "2.2.*", "hyperf/rpc-server": "2.2.*", "hyperf/socketio-server": "^2.2", "hyperf/task": "^2.2", "hyperf/translation": "^2.2", "hyperf/validation": "2.2.*", "hyperf/websocket-server": "^2.2", "league/flysystem-aws-s3-v3": "^2.5", "overtrue/pinyin": "^4.1", "phper666/jwt-auth": "^3.0.1", "phpoffice/phpspreadsheet": "^1.29", "volcengine/ve-tos-php-sdk": "^2.1", "yeepay/yop-php-sdk": "3.1.13"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "hyperf/devtool": "2.2.*", "hyperf/testing": "2.2.*", "hyperf/watcher": "^2.2", "mockery/mockery": "^1.0", "phpstan/phpstan": "^0.12", "swoole/ide-helper": "^4.5"}, "suggest": {"ext-openssl": "Required to use HTTPS.", "ext-json": "Required to use JSON.", "ext-pdo": "Required to use MySQL Client.", "ext-pdo_mysql": "Required to use MySQL Client.", "ext-redis": "Required to use Redis Client."}, "autoload": {"psr-4": {"App\\": "app/"}, "files": ["app/Helpers/Helpers.php"], "classmap": ["app/Libs/TripLink"]}, "autoload-dev": {"psr-4": {"HyperfTest\\": "./test/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": [], "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-autoload-dump": ["rm -rf runtime/container"], "test": "co-phpunit -c phpunit.xml --colors=always", "cs-fix": "php-cs-fixer fix $1", "analyse": "phpstan analyse --memory-limit 300M -l 0 -c phpstan.neon ./app ./config", "start": ["Composer\\Config::disableProcessTimeout", "php ./bin/hyperf.php start"]}}