<?php
/**
 * 中文
 */
return [
	'success'                                       => '成功',
	'error'                                         => '系统繁忙，请稍后再试',
	'privilege_grant_failed'                        => '授权失败',
	'request_expired'                               => '请求已过期',
	'sign_error'                                    => '签名验证失败',
	'flight_search_error'                           => '未查询到航班数据',
	'flight_search_cabin_error'                     => '未查询到舱位数据',
	'flight_distribution_error'                     => '销售商不一致',
	//查询航班相关翻译
	'search_for_five_courses'                       => '多程查询最多支持5程',
	'one_journey'                                   => '单程查询只能包含一个行程信息',
	'return_journey'                                => '往返查询行程信息需要两个行程',
	'return_date_required'                          => '返程日期不能为空',
	'return_date_after_or_equal'                    => '返程日期不能早于出发日期',
	'multiple_journey'                              => '往返或者多程应该包含多个行程信息',
	'departure_and_arrival_identical'               => '出发地和目的地不能相同',
	'max_people_number_is_nine'                     => '查询人数总和不能超过9人',
	'adult_outweigh_children'                       => '儿童人数不能大于成人人数',
	'adult_outweigh_infant'                         => '婴儿人数不能大于成人人数',
	'adult_child_infant_error'                      => '一个成人只能携带一个儿童或者婴儿',
	'trip_type_required'                            => '请选择行程类型',
	'trip_type_in'                                  => '行程类型错误',
	'cabin_class_required'                          => '请选择舱等',
	'cabin_class_in'                                => '舱等类型错误',
	'journeys_required'                             => '行程信息不能为空',
	'journeys_date_required'                        => '出行日期不能为空',
	'journeys_date_date'                            => '出行日期无效',
	'journeys_returnDate_date'                      => '返程日期无效',
	'journeys_date_after_or_equal'                  => '出行日期必须是今天或之后的日期',
	'journeys_origin_required'                      => '请选择出发地',
	'journeys_origin_not_exist'                     => '出发地不存在',
	'origin_type_required'                          => '出发地类型错误',
	'origin_type_in'                                => '出发地类型错误',
	'journeys_destination_required'                 => '请选择到达地',
	'journeys_destination_not_exist'                => '到达地不存在',
	'destination_type_required'                     => '到达地类型错误',
	'destination_type_in'                           => '到达地类型错误',
	'airline_required'                              => '改签航司不能为空',
	'adult_num_required'                            => '出行人类型必须包含成人',
	'adult_num_gt'                                  => '成人数量必须大于或等于1人',
	'adult_num_max'                                 => '成人数量最多为9人',
	'adult_num_integer'                             => '成人数量错误',
	'child_num_integer'                             => '儿童数量错误',
	'child_num_min'                                 => '儿童数量错误',
	'infant_num_integer'                            => '婴儿数量错误',
	'infant_num_min'                                => '婴儿数量错误',
	'airline_size'                                  => '航司二字码错误',
	'transferNumber_in'                             => '航段数量错误',
	//验价相关翻译
	'price_key_required'                            => '价格标识不能为空',
	'price_key_string'                              => '价格标识错误',
	'price_has_been_updated'                        => '价格已更新，请重新查询',
	'price_has_expired'                             => '价格已失效，请重新查询',
	//生单相关翻译
	'verifyKey_required'                            => '验价标识不能为空',
	'verifyKey_string'                              => '验价标识类型错误',
	'contactName_required'                          => '请填写联系人姓名',
	'contactName_string'                            => '联系人姓名类型错误',
	'contactRegion_required'                        => '联系人手机区号不能为空',
	'contactRegion_string'                          => '联系人手机区号类型错误',
	'contactPhone_required'                         => '请输入联系人手机号码',
	'contactPhone_error'                            => '联系人手机号码错误',
	'contactEmail_required'                         => '联系人邮箱不能为空',
	'contactEmail_email'                            => '联系人邮箱格式错误',
	'passenger_nationality_required'                => '国籍信息不能为空',
	'passenger_nationality_error'                   => '国籍与证件类型不匹配请检查',
	'passenger_uname_required'                      => '乘机人姓不能为空',
	'passenger_name_required'                       => '乘机人名字不能为空',
	'passenger_name_include_chinese'                => '乘机人姓名只能是中文',
	'passenger_name_cannot_include_chinese'         => '乘机人姓名不能包含中文',
	'international_flight_id_error'                 => '国际航班不支持身份证购买',
	'passenger_name_repeat'                         => '同一预定乘机人姓名不能重复预定',
	'passenger_email_email'                         => '乘机人邮箱填写格式错误',
	'passenger_gender_required'                     => '乘机人性别不能为空',
	'passenger_gender_in'                           => '乘机人性别类型错误',
	'passenger_idType_required'                     => '乘机人证件类型不能为空',
	'passenger_idType_in'                           => '乘机人证件类型错误',
	'passenger_idNumber_required'                   => '乘机人证件号码不能为空',
	'passenger_idNumber_length'                     => '乘机人证件号码不符合要求',
	'passenger_idNumber_repeat'                     => '同一预定乘机人证件号码不能重复预定',
	'passenger_birthday_required'                   => '乘机人出生日期不能为空',
	'passenger_birthday_date'                       => '乘机人出生日期不符合要求',
	'passenger_birthday_date_today_before'          => '乘机人出生日期不能是今天或之后的日期',
	'passenger_type_required'                       => '乘机人类型不能为空',
	'passenger_type_in'                             => '乘机人类型错误',
	'passenger_expiration_required'                 => '乘机人证件有效期不能为空',
	'passenger_expiration_date'                     => '乘机人证件有效期类型错误',
	'passenger_expiration_date_china_expire'        => '起飞时证件已过期，无法购票',
	'passenger_expiration_date_expire'              => '起飞时证件已过期或有效期小于半年，无法购票',
	'usable_identity_document_zf'                   => '该价格仅支持身份证、护照、港澳通行证、台湾通行证、台胞证、回乡证等证件购买',
	'usable_identity_document_pj'                   => '该价格仅支持护照和国际海员证书证件购买',
	'different_query_numbers'                       => '价格标识与乘机人类型不匹配',
	'create_order_error'                            => '生成订单失败，请稍后重试',
	'child_and_infant_bind_adult'                   => '儿童或则婴儿需要与成人绑定',
	'child_and_infant_bind_adult_error'             => '儿童或婴儿绑定的成人名称不存在',
	'child_and_infant_gt_adult'                     => '一个成人只能携带一个儿童或者婴儿',
	//支付相关翻译
	'orderNo_required'                              => '订单编号不能为空',
	'amount_required'                               => '订单金额不能为空',
	'order_cancelled_unable_pay'                    => '订单已取消，不能支付',
	'order_payment'                                 => '订单已支付，请勿重复操作',
	'order_payment_failed'                          => '订单支付失败，请稍后再试',
	'order_under_review'                            => '订单审核中，请审核通过后再试',
	'order_audit_failure'                           => '订单审核失败',
	'order_price_update'                            => '订单支付金额错误',
	'pay_type_required'                             => '支付订单类型不能为空',
	'pay_type_in'                                   => '支付订单类型有误',
	//订单相关
	'order_does_not_exist'                          => '未查询到订单信息',
	'order_invalid'                                 => '订单已失效',
	'cancel_order_failed'                           => '取消订单失败,请稍后再试',
	'order_payment_received'                        => '订单已付款，不能取消',
	//退票相关
	'refund_order_not_exists'                       => '未查询到退票订单信息',
	'refund_order_complete'                         => '订单未完成，无法退票',
	'refund_order_invalid'                          => '退票订单已失效',
	'refund_order_already_exists'                   => '已存在退票订单，请勿重复申请',
	'refund_order_error'                            => '提交退票失败',
	'refund_order_passenger_error'                  => '未查询到乘机人信息',
	'refund_order_passenger_child_infant_error'     => '成人退票时需要将携带的儿童或者婴儿一并退票，请重新申请退票',
	'refund_order_already_exists_and_not_completed' => '已存在退票订单，请等待退票结果后再次提交',
	'refund_order_completed'                        => '已退票，请勿重复操作',
	'refund_order_already_change'                   => '退票信息中存在已经改签的乘机人信息，请检查',
	'refund_order_ticket_no_not_exists'             => '未查询到乘机人票号信息',
	'refund_order_type_required'                    => '退票订单类型不能为空',
	'refund_order_type_in'                          => '退票订单类型错误',
	'refund_type_required'                          => '是否自愿退票不能为空',
	'refund_type_in'                                => '是否自愿退票类型错误',
	'reason_type_required'                          => '退票原因类型不能为空',
	'reason_type_in_required'                       => '退票原因类型错误',
	'refund_order_cancel_error'                     => '取消退票失败',
	'refund_order_cancel_afoot_error'               => '退票中，不能取消',
	'refund_order_completed_error'                  => '已退票,无法操作',
	'refund_order_cancel_completed_error'           => '已取消,请勿重复操作',
	'refund_order_confirm_error'                    => '当前退票单状态不能进行确认退票操作',
	'confirm_required'                              => '确认退票类型不能为空',
	'confirm_in'                                    => '确认退票类型错误',
	//改签相关
	'change_order_not_exists'                       => '未查询到改签订单信息',
	'change_order_not_complete_ota'                 => '未查询到可改签的订单信息',
	'change_order_already_exists'                   => '已存在改签订单，请勿重复申请',
	'change_order_error'                            => '提交改签申请失败',
	'change_order_passenger_not_exists'             => '未查询到改签乘机人信息',
	'change_order_ticket_no_not_exists'             => '未查询到改签票号信息',
	'change_order_cancel_error'                     => '取消改签申请失败',
	'change_order_cancel_cancel'                    => '该订单已被驳回或者已取消',
	'change_order_payment_received'                 => '改签申请已支付，不能取消',
	'change_order_in_progress'                      => '当前状态不能进行支付，请稍后再试',
	'change_order_price_error'                      => '支付金额错误，请重新支付',
	'change_child_infant'                           => '儿童或者婴儿需要和绑定的成人一起改签',
	'change_order_not_last'                         => '该订单已改签，不能操作',
	'change_order_not_complete'                     => '还存在改签单未完成，不能再次改签',
	'change_order_passenger_child_infant_error'     => '成人改签时需要将携带的儿童或者婴儿一并改签，请重新申请',
	'change_order_exists_refund_order'              => '已存在退票单，无法操作',
	'change_search_invalid'                         => '改签查询已过期，请重新查询',
	'passenger_required'                            => '乘机人信息不能为空',
	'segmentId_required'                            => '改签航段不能为空',
	'change_sequence_error'                         => '航段信息异常',
	'change_sequence_not_exists'                    => '未查询到航段信息',
	'change_sequence_separate_error'                => '暂不支持往返一起改签，请分开提交',
	'change_sequence_multiclass_error'              => '多程改签暂不支持同时改签多行程，请分开提交',
	'changeOrderType_required'                      => '改签来源不能为空',
	'changeOrderType_in'                            => '改签来源错误',
	'changeType_required'                           => '改签类型不能为空',
	'changeType_in'                                 => '改签类型错误',
	'changeReasonType_required'                     => '是否自愿改签不能为空',
	'changeReasonType_in'                           => '是否自愿改签错误',
	'changeReason_required'                         => '改签原因不能为空',
	//获取退改签信息错误信息
	'refund_change_info_error'                      => '获取退改签信息失败，请稍后再试',
	'refund_change_not_exists'                      => '查询已过期，请重新进行航班查询后再试',
	'refund_change_sequence_error'                  => '多段行程，请一起退票或按顺序进行退票',
	//机场接口数据校验
	'page_required'                                 => '当前页页码不能为空',
	'page_integer'                                  => '当前页页码错误',
	'page_min'                                      => '当前页页码最小为1',
	'pageSize_required'                             => '当前页显示数量不能为空',
	'pageSize_integer'                              => '当前页显示数量错误',
	'pageSize_min'                                  => '当前页显示数量最小为1',
	'internationality_integer'                      => '国际/国内参数错误',
	'internationality_in'                           => '国际/国内参数错误',
    'keyword_required'                              => '关键词不能为空'
];
