<?php
/**
 * 英文翻译
 */
return [
    'success'                                       => 'successful', // 成功
    'error'                                         => 'System busy, please try again later', // 系统繁忙，请稍后再试
    'privilege_grant_failed'                        => 'Authorization failed', // 授权失败
    'request_expired'                               => 'Request expired', // 请求已过期
    'sign_error'                                    => 'Signature verification failed', // 签名验证失败
    'flight_search_error'                           => 'No flight data found', // 未查询到航班数据
    'flight_search_cabin_error'                     => 'No cabin data found', // 未查询到舱位数据
    'flight_distribution_error'                     => 'Distributor mismatch', // 销售商不一致
    //查询航班相关翻译
    'search_for_five_courses'                       => 'Multi-segment search supports up to 5 segments', // 多程查询最多支持5程
    'one_journey'                                   => 'One-way search can only contain one itinerary', // 单程查询只能包含一个行程信息
    'return_journey'                                => 'Round-trip search requires two itineraries', // 往返查询行程信息需要两个行程
    'return_date_required'                          => 'Return date cannot be empty', // 返程日期不能为空
    'return_date_after_or_equal'                    => 'Return date cannot be earlier than departure date', // 返程日期不能早于出发日期
    'multiple_journey'                              => 'Round-trip or multi-segment should contain multiple itineraries', // 往返或者多程应该包含多个行程信息
    'departure_and_arrival_identical'               => 'Departure and destination cannot be the same', // 出发地和目的地不能相同
    'max_people_number_is_nine'                      => 'Total number of passengers cannot exceed 9', // 查询人数总和不能超过9人
    'adult_outweigh_children'                       => 'Number of children cannot exceed adults', // 儿童人数不能大于成人人数
    'adult_outweigh_infant'                         => 'Number of infants cannot exceed adults', // 婴儿人数不能大于成人人数
    'adult_child_infant_error'                      => 'One adult can only carry one child or infant', // 一个成人只能携带一个儿童或者婴儿
    'trip_type_required'                            => 'Please select trip type', // 请选择行程类型
    'trip_type_in'                                  => 'Invalid trip type', // 行程类型错误
    'cabin_class_required'                          => 'Please select cabin class', // 请选择舱等
    'cabin_class_in'                                => 'Invalid cabin class', // 舱等类型错误
    'journeys_required'                             => 'Itinerary information cannot be empty', // 行程信息不能为空
    'journeys_date_required'                        => 'Travel date cannot be empty', // 出行日期不能为空
    'journeys_date_date'                            => 'Invalid travel date', // 出行日期无效
    'journeys_returnDate_date'                      => 'Invalid return date', // 返程日期无效
    'journeys_date_after_or_equal'                  => 'Travel date must be today or later', // 出行日期必须是今天或之后的日期
    'journeys_origin_required'                      => 'Please select departure city', // 请选择出发地
    'journeys_origin_not_exist'                     => 'Departure city does not exist', // 出发地不存在
    'origin_type_required'                          => 'Invalid departure city type', // 出发地类型错误
    'origin_type_in'                                => 'Invalid departure city type', // 出发地类型错误
    'journeys_destination_required'                 => 'Please select destination city', // 请选择到达地
    'journeys_destination_not_exist'                => 'Destination city does not exist', // 到达地不存在
    'destination_type_required'                     => 'Invalid destination city type', // 到达地类型错误
    'destination_type_in'                           => 'Invalid destination city type', // 到达地类型错误
    'airline_required'                              => 'Change airline cannot be empty', // 改签航司不能为空
    'adult_num_required'                            => 'Passenger type must include adults', // 出行人类型必须包含成人
    'adult_num_gt'                                  => 'Number of adults must be greater than or equal to 1', // 成人数量必须大于或等于1人
    'adult_num_max'                                 => 'Maximum number of adults is 9', // 成人数量最多为9人
    'adult_num_integer'                             => 'Invalid number of adults', // 成人数量错误
    'child_num_integer'                             => 'Invalid number of children', // 儿童数量错误
    'child_num_min'                                 => 'Invalid number of children', // 儿童数量错误
    'infant_num_integer'                            => 'Invalid number of infants', // 婴儿数量错误
    'infant_num_min'                                => 'Invalid number of infants', // 婴儿数量错误
    'airline_size'                                  => 'Invalid airline code', // 航司二字码错误
    'transferNumber_in'                             => 'Invalid segment count', // 航段数量错误
    //验价相关翻译
    'price_key_required'                            => 'Price key cannot be empty', // 价格标识不能为空
    'price_key_string'                              => 'Invalid price key', // 价格标识错误
    'price_has_been_updated'                        => 'Price has been updated, please search again', // 价格已更新，请重新查询
    'price_has_expired'                             => 'Price has expired, please search again', // 价格已失效，请重新查询
    //生单相关翻译
    'verifyKey_required'                            => 'Verification key cannot be empty', // 验价标识不能为空
    'verifyKey_string'                              => 'Invalid verification key type', // 验价标识类型错误
    'contactName_required'                          => 'Please enter contact name', // 请填写联系人姓名
    'contactName_string'                            => 'Invalid contact name type', // 联系人姓名类型错误
    'contactRegion_required'                        => 'Contact phone area code cannot be empty', // 联系人手机区号不能为空
    'contactRegion_string'                          => 'Invalid contact phone area code type', // 联系人手机区号类型错误
    'contactPhone_required'                         => 'Please enter contact phone number', // 请输入联系人手机号码
    'contactPhone_error'                            => 'Invalid contact phone number', // 联系人手机号码错误
    'contactEmail_required'                         => 'Contact email cannot be empty', // 联系人邮箱不能为空
    'contactEmail_email'                            => 'Invalid contact email format', // 联系人邮箱格式错误
    'passenger_nationality_required'                => 'Nationality information cannot be empty', // 国籍信息不能为空
    'passenger_nationality_error'                   => 'Nationality does not match ID type, please check', // 国籍与证件类型不匹配请检查
    'passenger_uname_required'                      => 'Passenger surname cannot be empty', // 乘机人姓不能为空
    'passenger_name_required'                       => 'Passenger given name cannot be empty', // 乘机人名字不能为空
    'passenger_name_include_chinese'                => 'Passenger name must be in Chinese', // 乘机人姓名只能是中文
    'passenger_name_cannot_include_chinese'         => 'Passenger name cannot contain Chinese characters', // 乘机人姓名不能包含中文
    'international_flight_id_error'                 => 'ID card not supported for international flights', // 国际航班不支持身份证购买
    'passenger_name_repeat'                         => 'Duplicate passenger name in same booking', // 同一预定乘机人姓名不能重复预定
    'passenger_email_email'                         => 'Invalid passenger email format', // 乘机人邮箱填写格式错误
    'passenger_gender_required'                     => 'Passenger gender cannot be empty', // 乘机人性别不能为空
    'passenger_gender_in'                           => 'Invalid passenger gender type', // 乘机人性别类型错误
    'passenger_idType_required'                     => 'Passenger ID type cannot be empty', // 乘机人证件类型不能为空
    'passenger_idType_in'                           => 'Invalid passenger ID type', // 乘机人证件类型错误
    'passenger_idNumber_required'                   => 'Passenger ID number cannot be empty', // 乘机人证件号码不能为空
    'passenger_idNumber_length'                     => 'Passenger ID number does not meet requirements', // 乘机人证件号码不符合要求
    'passenger_idNumber_repeat'                     => 'Duplicate passenger ID number in same booking', // 同一预定乘机人证件号码不能重复预定
    'passenger_birthday_required'                   => 'Passenger birth date cannot be empty', // 乘机人出生日期不能为空
    'passenger_birthday_date'                       => 'Invalid passenger birth date', // 乘机人出生日期不符合要求
    'passenger_birthday_date_today_before'          => 'Passenger birth date cannot be today or future date', // 乘机人出生日期不能是今天或之后的日期
    'passenger_type_required'                       => 'Passenger type cannot be empty', // 乘机人类型不能为空
    'passenger_type_in'                             => 'Invalid passenger type', // 乘机人类型错误
    'passenger_expiration_required'                 => 'Passenger ID expiration date cannot be empty', // 乘机人证件有效期不能为空
    'passenger_expiration_date'                     => 'Invalid passenger ID expiration date type', // 乘机人证件有效期类型错误
    'passenger_expiration_date_china_expire'        => 'ID expired at departure time, cannot purchase ticket', // 起飞时证件已过期，无法购票
    'passenger_expiration_date_expire'              => 'ID expired or validity less than 6 months at departure, cannot purchase ticket', // 起飞时证件已过期或有效期小于半年，无法购票
    'usable_identity_document_zf'                   => 'This fare only supports ID card, passport, HK/Macau pass, Taiwan pass, Mainland Travel Permit, Home Return Permit', // 该价格仅支持身份证、护照、港澳通行证、台湾通行证、台胞证、回乡证等证件购买
    'usable_identity_document_pj'                   => 'This fare only supports passport and international seafarer certificate', // 该价格仅支持护照和国际海员证书证件购买
    'different_query_numbers'                       => 'Price key does not match passenger type', // 价格标识与乘机人类型不匹配
    'create_order_error'                            => 'Failed to create order, please try again later', // 生成订单失败，请稍后重试
    'child_and_infant_bind_adult'                   => 'Children or infants must be bound to an adult', // 儿童或则婴儿需要与成人绑定
    'child_and_infant_bind_adult_error'             => 'Bound adult name does not exist', // 儿童或婴儿绑定的成人名称不存在
    'child_and_infant_gt_adult'                     => 'One adult can only carry one child or infant', // 一个成人只能携带一个儿童或者婴儿
    //支付相关翻译
    'orderNo_required'                              => 'Order number cannot be empty', // 订单编号不能为空
    'amount_required'                               => 'Order amount cannot be empty', // 订单金额不能为空
    'order_cancelled_unable_pay'                    => 'Order cancelled, cannot proceed with payment', // 订单已取消，不能支付
    'order_payment'                                 => 'Order already paid, do not repeat operation', // 订单已支付，请勿重复操作
    'order_payment_failed'                          => 'Order payment failed, please try again later', // 订单支付失败，请稍后再试
    'order_under_review'                            => 'Order under review, please try again after approval', // 订单审核中，请审核通过后再试
    'order_audit_failure'                           => 'Order audit failed', // 订单审核失败
    'order_price_update'                            => 'Incorrect order payment amount', // 订单支付金额错误
    'pay_type_required'                             => 'Payment order type cannot be empty', // 支付订单类型不能为空
    'pay_type_in'                                   => 'Invalid payment order type', // 支付订单类型有误
    //订单相关
    'order_does_not_exist'                          => 'Order information not found', // 未查询到订单信息
    'order_invalid'                                 => 'Order expired', // 订单已失效
    'cancel_order_failed'                           => 'Failed to cancel order, please try again later', // 取消订单失败,请稍后再试
    'order_payment_received'                        => 'Order already paid, cannot cancel', // 订单已付款，不能取消
    //退票相关
    'refund_order_not_exists'                       => 'Refund order information not found', // 未查询到退票订单信息
    'refund_order_complete'                         => 'Order not completed, cannot refund', // 订单未完成，无法退票
    'refund_order_invalid'                          => 'Refund order expired', // 退票订单已失效
    'refund_order_already_exists'                   => 'Refund order already exists, do not reapply', // 已存在退票订单，请勿重复申请
    'refund_order_error'                            => 'Failed to submit refund request', // 提交退票失败
    'refund_order_passenger_error'                  => 'Passenger information not found', // 未查询到乘机人信息
    'refund_order_passenger_child_infant_error'     => 'When refunding adult ticket, must refund bound child/infant together, please reapply', // 成人退票时需要将携带的儿童或者婴儿一并退票，请重新申请退票
    'refund_order_already_exists_and_not_completed' => 'Refund order exists, please wait for result before resubmitting', // 已存在退票订单，请等待退票结果后再次提交
    'refund_order_completed'                        => 'Already refunded, do not repeat operation', // 已退票，请勿重复操作
    'refund_order_already_change'                   => 'Refund contains passengers with changed flights, please check', // 退票信息中存在已经改签的乘机人信息，请检查
    'refund_order_ticket_no_not_exists'             => 'Passenger ticket number not found', // 未查询到乘机人票号信息
    'refund_order_type_required'                    => 'Refund order type cannot be empty', // 退票订单类型不能为空
    'refund_order_type_in'                          => 'Invalid refund order type', // 退票订单类型错误
    'refund_type_required'                          => 'Voluntary refund status cannot be empty', // 是否自愿退票不能为空
    'refund_type_in'                                => 'Invalid voluntary refund status', // 是否自愿退票类型错误
    'reason_type_required'                          => 'Refund reason type cannot be empty', // 退票原因类型不能为空
    'reason_type_in_required'                       => 'Invalid refund reason type', // 退票原因类型错误
    'refund_order_cancel_error'                     => 'Failed to cancel refund request', // 取消退票失败
    'refund_order_cancel_afoot_error'               => 'Refund in progress, cannot cancel', // 退票中，不能取消
    'refund_order_completed_error'                  => 'Already refunded, cannot operate', // 已退票,无法操作
    'refund_order_cancel_completed_error'           => 'Already cancelled, do not repeat operation', // 已取消,请勿重复操作
    'refund_order_confirm_error'                    => 'The current status of the ticket refund form cannot be confirmed for refund operation', // 当前退票单状态不能进行确认退票操作
    'confirm_required'                              => 'Refund confirmation type cannot be empty', // 确认退票类型不能为空
    'confirm_in'                                    => 'Invalid refund confirmation type', // 确认退票类型错误
    //改签相关
    'change_order_not_exists'                       => 'Change order information not found', // 未查询到改签订单信息
    'change_order_not_complete_ota'                 => 'No changeable order found', // 未查询到可改签的订单信息
    'change_order_already_exists'                   => 'Change order already exists, do not reapply', // 已存在改签订单，请勿重复申请
    'change_order_error'                            => 'Failed to submit change request', // 提交改签申请失败
    'change_order_passenger_not_exists'             => 'Passenger information for change not found', // 未查询到改签乘机人信息
    'change_order_ticket_no_not_exists'             => 'Change ticket number not found', // 未查询到改签票号信息
    'change_order_cancel_error'                     => 'Failed to cancel change request', // 取消改签申请失败
    'change_order_cancel_cancel'                    => 'Order already rejected or cancelled', // 该订单已被驳回或者已取消
    'change_order_payment_received'                 => 'Change request already paid, cannot cancel', // 改签申请已支付，不能取消
    'change_order_in_progress'                      => 'Cannot proceed with payment in current status, please try again later', // 当前状态不能进行支付，请稍后再试
    'change_order_price_error'                      => 'Incorrect payment amount, please pay again', // 支付金额错误，请重新支付
    'change_child_infant'                           => 'Children or infants must change with bound adult', // 儿童或者婴儿需要和绑定的成人一起改签
    'change_order_not_last'                         => 'Order already processed, cannot operate', // 该订单已改签，不能操作
    'change_order_not_complete'                     => 'Uncompleted change order exists, cannot change again', // 还存在改签单未完成，不能再次改签
    'change_order_passenger_child_infant_error'     => 'When changing adult ticket, must change bound child/infant together, please reapply', // 成人改签时需要将携带的儿童或者婴儿一并改签，请重新申请
    'change_order_exists_refund_order'              => 'Refund order exists, cannot operate', // 已存在退票单，无法操作
    'change_search_invalid'                         => 'Change query expired, please search again', // 改签查询已过期，请重新查询
    'passenger_required'                            => 'Passenger information cannot be empty', // 乘机人信息不能为空
    'segmentId_required'                            => 'Change segment cannot be empty', // 改签航段不能为空
    'change_sequence_error'                         => 'Segment information error', // 航段信息异常
    'change_sequence_not_exists'                    => 'No segment information found', // 未查询到航段信息
    'change_sequence_separate_error'                => 'Round-trip changes are not supported together, please submit separately', // 暂不支持往返一起改签，请分开提交
    'change_sequence_multiclass_error'              => 'Multi trip rescheduling is currently not supported for simultaneous rescheduling of multiple trips. Please submit separately', // 多程改签暂不支持同时改签多段行程，请分开提交
    'changeOrderType_required'                      => 'Change source cannot be empty', // 改签来源不能为空
    'changeOrderType_in'                            => 'Invalid change source', // 改签来源错误
    'changeType_required'                           => 'Change type cannot be empty', // 改签类型不能为空
    'changeType_in'                                 => 'Invalid change type', // 改签类型错误
    'changeReasonType_required'                     => 'Voluntary change status cannot be empty', // 是否自愿改签不能为空
    'changeReasonType_in'                           => 'Invalid voluntary change status', // 是否自愿改签错误
    'changeReason_required'                         => 'Change reason cannot be empty', // 改签原因不能为空
    //获取退改签信息错误信息
    'refund_change_info_error'                      => 'Failed to get refund/change information, please try again later', // 获取退改签信息失败，请稍后再试
    'refund_change_not_exists'                      => 'Query expired, please search flights again', // 查询已过期，请重新进行航班查询后再试
    'refund_change_sequence_error'                  => 'Multi-segment itinerary, please refund all segments together or in sequence', // 多段行程，请一起退票或按顺序进行退票
    //机场接口数据校验
    'page_required'                                 => 'Page number cannot be empty', // 当前页页码不能为空
    'page_integer'                                  => 'Invalid page number', // 当前页页码错误
    'page_min'                                      => 'Minimum page number is 1', // 当前页页码最小为1
    'pageSize_required'                             => 'Page size cannot be empty', // 当前页显示数量不能为空
    'pageSize_integer'                              => 'Invalid page size', // 当前页显示数量错误
    'pageSize_min'                                  => 'Minimum page size is 1', // 当前页显示数量最小为1
    'internationality_integer'                      => 'Invalid international/domestic parameter', // 国际/国内参数错误
    'internationality_in'                           => 'Invalid international/domestic parameter', // 国际/国内参数错误
    'keyword_required'                              => 'Keyword cannot be empty', // 关键词不能为空
];
