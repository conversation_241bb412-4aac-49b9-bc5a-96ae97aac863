<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 80 80" style="enable-background:new 0 0 80 80;" xml:space="preserve">
<style type="text/css">
	.st0{filter:url(#Adobe_OpacityMaskFilter);}
	.st1{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st2{mask:url(#mask-2_00000083789393471100316760000005710383358570071223_);fill-rule:evenodd;clip-rule:evenodd;fill:#BC1427;}
</style>
<g id="Group-21">
	<g id="Clip-20">
	</g>
	<defs>
		<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="0" y="0" width="80" height="80">
			<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
		</filter>
	</defs>
	
		<mask maskUnits="userSpaceOnUse" x="0" y="0" width="80" height="80" id="mask-2_00000083789393471100316760000005710383358570071223_">
		<g class="st0">
			<polygon id="path-1_00000145056775728327793900000002101889542506465693_" class="st1" points="0,0 80,0 80,80 0,80 			"/>
		</g>
	</mask>
	<polygon id="Fill-19" class="st2" points="63.49,40 79.71,23.14 40.43,39.59 56.16,0 39.99,16.5 23.15,0.29 39.63,39.59 0,23.84 
		16.49,40 0.29,56.85 39.63,40.39 23.84,80 39.99,63.49 56.85,79.7 40.43,40.39 80,56.16 	"/>
</g>
</svg>
