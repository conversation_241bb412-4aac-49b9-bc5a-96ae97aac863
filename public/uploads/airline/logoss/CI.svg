<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 26.5.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_2_00000152971299375307541800000009735777370349640325_"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 80 80"
	 style="enable-background:new 0 0 80 80;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#A12132;}
	.st1{fill:#FFFFFF;}
	.st2{fill-rule:evenodd;clip-rule:evenodd;fill:#CA5766;}
	.st3{clip-path:url(#SVGID_00000043446131706235656950000014374895655854836628_);}
	.st4{fill:url(#SVGID_00000073718330130800086320000005103242799838138020_);}
	.st5{clip-path:url(#SVGID_00000181080530467938129950000010756175779287748259_);}
	.st6{fill:url(#SVGID_00000002349048046811427690000005428714632678563714_);}
	.st7{clip-path:url(#SVGID_00000025417185955602542130000001674571698963921563_);}
	.st8{fill:url(#SVGID_00000106841026556483724420000010539392847792465085_);}
	.st9{clip-path:url(#SVGID_00000052094018693282413220000007299925068147604410_);}
	.st10{fill:url(#SVGID_00000118384506211393601810000005954480048940887690_);}
	.st11{clip-path:url(#SVGID_00000171703091692953626820000003611084311528619151_);}
	.st12{fill:url(#SVGID_00000046301282834490751370000007771424729983948970_);}
	.st13{clip-path:url(#SVGID_00000042732326201353614870000006739119995835051667_);}
	.st14{fill:url(#SVGID_00000062902973527254320460000000596239681306628241_);}
	.st15{clip-path:url(#SVGID_00000029765007577717477830000012893886150699362700_);}
	.st16{fill:url(#SVGID_00000177474904180616920960000011742677725783314604_);}
	.st17{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st18{fill-rule:evenodd;clip-rule:evenodd;fill:#A12132;}
</style>
<g>
	<path class="st0" d="M9.6,53.6c0.8,3.5,0.7,9.3-1.7,12.2c-3.4,4.1-6.6-0.4-7.5-3.9c-0.9-3.7-0.8-10.2,2-13.1c0.9-1.2,2.4-1.9,3.8-1
		C8.4,49.3,8.9,51.2,9.6,53.6"/>
	<path class="st1" d="M6.5,58.1c0.1,0.4-0.2,1,0.4,1.2c0.4,0.4,1.5,0.2,1.4,0.9c-0.8,0-1.5-0.8-2.4-0.6C5.4,59.7,5,60,4.5,60.2
		c-0.1-0.1-0.1-0.1-0.1-0.2c0.5-0.4,1.2-0.4,1.7-0.8v-1.2C6.2,57.6,6.5,57.9,6.5,58.1"/>
	<path class="st1" d="M7.3,60.8c0.5,0.5,0.1,1,0.2,1.7c-0.1,1.2,0,2.4,0.3,3.6c0,0.2-0.1,0.3-0.2,0.4c-0.2,0-0.2-0.1-0.3-0.2
		c-0.1-1.7-0.3-3.3-0.3-5c-0.1-0.4-0.7-0.1-1-0.2c-0.2,0-0.3,0.1-0.4,0.2c0,1.7-0.1,3.4-0.3,5.1c-0.1,0.1-0.1,0.1-0.2,0.1L5,66.3
		c0.1-1.7,0.1-3.6,0.2-5.2c0.2-0.3,0.6-0.5,1-0.5C6.6,60.6,7,60.7,7.3,60.8"/>
	<path class="st1" d="M3.6,63.6c0,0.4,0,0.7,0,1c0,0.4-0.1,0.8-0.2,1.2c0,0.2-0.2,0.4-0.1,0.6h0.2c0.3-0.2,0.3-0.6,0.3-0.9
		c0-0.3,0-0.7,0.1-1c0-0.7,0-1.4,0.1-2.1c0-0.1,0-0.3,0-0.4c0.1-0.7,0.1-1.3-0.1-2c-0.2-0.6-0.9-0.7-1.5-0.8
		c-0.1-0.1-0.2-0.3-0.2-0.4c0.2-0.3,0.4-0.4,0.8-0.4c0.1,0,0.2-0.1,0.3-0.1c0.1-0.1,0.3-0.1,0.4-0.1C3.9,58.1,4,58,4,57.9
		c-0.2-0.4-0.5,0-0.8,0C2.8,58,2.1,58,2,58.6c0,0.4,0.3,0.8,0.7,0.8c0,0,0,0,0,0c0.3,0.1,0.8,0.1,1,0.5c0,0.2,0,0.6,0,0.7
		c0,0.1,0,0.3,0,0.4c0,0.2,0,0.5,0,0.7c-0.1,0.2-0.4,0.1-0.6,0.1c-0.2,0-0.4,0-0.5-0.1c-0.1-0.2-0.1-0.4-0.1-0.6
		c0.3-0.2,0.6-0.1,0.9-0.3c0,0,0,0,0-0.1c0,0,0-0.1,0-0.1l-0.1-0.1c-0.2-0.2-0.3,0-0.6,0H2.7c-0.4,0-0.1-0.6-0.3-0.8
		c-0.1,0-0.1-0.2-0.3-0.2c-0.3,0.2,0,0.6,0,0.9c0.1,0.9,0.1,1.7-0.1,2.6c0,0.4,0,0.9-0.1,1.3c-0.1,0.2,0,0.4-0.1,0.5
		c-0.1,0.2-0.2,0.5,0,0.6C2,65.5,2,65.5,2.1,65.4c0.1-0.6,0.2-1.1,0.3-1.7c0-0.1,0.1-0.2,0.2-0.2c0,0,0,0,0,0c0.3,0,0.3,0.2,0.6,0.2
		s0.1-0.4,0.1-0.4c-0.3,0-0.3-0.1-0.6-0.2c-0.1,0-0.3,0-0.2-0.1c0-0.2,0-0.4,0.1-0.6c0.3,0,0.5,0,0.8,0c0.1,0,0.3,0,0.4,0.1
		C3.7,62.9,3.7,63.3,3.6,63.6"/>
	<path class="st1" d="M8.4,53.7L8,53.6c-0.1-0.1-0.3-0.2-0.5-0.3c-0.4-0.2-0.8-0.5-1.2-0.8c-0.3,0-0.5,0.3-0.8,0.5
		c0,0-0.1,0.1-0.2,0c0.1-0.3,0.4-0.4,0.6-0.6c0.1-0.1,0.3-0.2,0.4-0.3c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1,0.4,0.3,0.7,0.4
		c0.2,0.1,0.4,0.2,0.6,0.3C8.3,53.4,8.6,53.3,8.4,53.7L8.4,53.7z"/>
	<path class="st1" d="M7.8,49.7c0,0.1-0.2,0.2-0.4,0.2S7.1,50,6.9,50.1c-0.1,0.1-0.1,0.2-0.1,0.2c0,0.2,0.3,0.2,0.5,0.3
		c0.1,0,0.1,0.1,0.1,0.1c0,0,0,0,0,0c-0.1,0.1-0.2,0-0.3,0c-0.3,0.1-0.6-0.1-0.9,0c-0.1,0-0.1,0-0.3,0.1c-0.1,0-0.1,0-0.2,0
		c-0.2,0.1-0.5,0-0.6,0.2C5,51.3,5,51.5,5,51.8c0,0.1,0,0.2-0.1,0.3c0,0.4,0.1,0.8,0.1,1.2c0,0.1,0,0.2,0,0.3
		c0.2,0.2,0.5,0.1,0.6,0.1c0.3,0,0.4,0,0.6,0c0.2,0,0.4,0,0.6,0c0,0,0.1,0,0.1,0.1c0,0-0.1,0.1-0.2,0.1c-0.2,0-0.4,0-0.5,0
		C5.9,53.9,5.5,54,5,54c-0.2,0-0.4,0-0.7,0c0,0-0.3,0-0.3,0c-0.2,0-0.4,0-0.6,0c-0.1,0-0.2,0-0.2-0.1c0-0.1,0.1-0.1,0.1-0.1H4
		c0.2,0,0.5,0,0.7-0.1c0-0.1,0-0.2,0-0.2c0-0.3-0.1-0.7,0-1.1c0-0.2,0-0.5,0-0.8c0-0.2,0-0.7,0.2-0.8l0.7-0.2c0.2,0,0.4,0,0.7,0
		c0.2,0,0.3-0.1,0.4-0.2c0-0.1-0.1-0.3-0.2-0.3c-0.2,0-0.1,0-0.4,0s-0.2,0-0.3-0.1c-0.2-0.3-0.3-0.7-0.2-1.1c0-0.1,0.1-0.2,0.2-0.1
		s0,0.3,0,0.5c0,0.3,0.1,0.5,0.3,0.6c0.1,0,0.3,0,0.4,0c0.2-0.1,0.1-0.4,0.1-0.5c0-0.2,0-0.5,0.1-0.7c0.1,0,0.1,0.1,0.2,0.2v0.1
		c0,0.3,0,0.6,0.1,0.8C7,49.8,7,49.8,7.1,49.8c0.1,0,0.3-0.2,0.4-0.3c0-0.2-0.1-0.6,0.2-0.6c0.1,0,0.1,0,0.1,0.1L7.8,49.7z"/>
	<path class="st1" d="M7.8,52.2c-0.1,0-0.2,0-0.3-0.1c-0.2-0.3-0.8-0.4-1-0.5c-0.2-0.2-0.3,0-0.5,0.1c-0.1,0.1-0.2,0.2-0.4,0.3
		c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0-0.1-0.1-0.1-0.1c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.1,0.5-0.3,0.7-0.5c0.2,0.1,0.7,0.3,0.9,0.4
		c0.2,0.1,0.5,0.3,0.6,0.5C7.9,52.2,7.8,52.2,7.8,52.2"/>
	<path class="st1" d="M6.7,55.9c0.1,0.3,0,0.7-0.1,1c-0.1,0.1-0.2,0.2-0.4,0.2c-0.4,0-0.3,0.1-0.7,0.1l-0.6,0c-0.4,0-0.7,0-1.1,0
		c-0.2,0-0.4,0-0.6-0.1C3,57.1,2.9,57.1,2.9,57c0-0.1,0.3-0.1,0.4-0.1c0.4,0,0.8,0.1,1.2,0.1c0.4,0,0.5,0,1,0c0.1,0,0.3-0.1,0.4-0.1
		c0.4-0.1,0.3-0.1,0.4-0.5s0-0.4-0.3-0.5s-0.7,0-0.9-0.3c-0.2-0.3,0-0.6-0.3-0.7c-0.3,0.1-0.6,0.1-0.9,0.1c-0.2,0-0.3,0-0.5,0
		c-0.1,0-0.3,0-0.4,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2-0.1-0.1-0.2c0.1-0.1,0.3,0,0.4,0c0.3,0,0.6,0,0.9,0c0.1,0,0.2,0,0.4,0
		c0.3,0,0.6,0,0.9,0c0.4,0,0.5,0,0.7,0c0.1,0,0.3,0,0.4,0c0.2,0,0.4,0,0.6,0c0.3,0,0.4-0.1,0.6,0c0.1,0,0.1,0.1,0.2,0.2
		c0,0,0,0.1-0.1,0.1c-0.2,0.1-0.4,0-0.7,0s-0.4,0-0.7,0c-0.2,0-0.4,0-0.6-0.1c-0.1,0-0.3,0-0.4,0c0,0.2,0,0.4,0.2,0.5
		c0.2,0.1,0.5,0.1,0.7,0.1C6.3,55.7,6.6,55.7,6.7,55.9"/>
	<path class="st1" d="M4.9,48.9c0,0.3,0.1,0.9-0.3,1.2c-0.3,0.1-0.6,0-0.8,0.2c-0.2,0.3,0,0.5,0,0.7c0.1,0.3,0.4,0.4,0.5,0.6
		c0.1,0.1,0.2,0.2,0.2,0.3c-0.1,0-0.1,0.1-0.2,0l-0.1-0.1l-0.1-0.1c-0.1-0.1-0.2-0.2-0.3-0.3c-0.5,0-0.7,0.4-1.1,0.6l-0.4,0.3
		l-0.2,0.2c-0.1,0.1-0.3,0.2-0.4,0.3v-0.1c0-0.1,0.2-0.2,0.3-0.2c0.2-0.3,0.5-0.6,0.8-0.8c0.2-0.2,0.6-0.4,0.7-0.7
		c0-0.1-0.1-0.2-0.1-0.3c0-0.2,0.1-0.4-0.1-0.5c-0.2,0-0.5-0.1-0.7-0.2c-0.3-0.4-0.4-0.9-0.2-1.4h0c0.2,0.1,0.1,0.3,0.1,0.5
		c0,0.3,0.1,0.5,0.2,0.7C2.9,49.9,3,50,3.2,50c0.1,0,0.2-0.1,0.2-0.2c0.1-0.3-0.1-0.6,0.1-1c0-0.1,0-0.2,0.1-0.3l0.2,0
		c0,0.2-0.1,0.4-0.1,0.6c0,0.2,0,0.4,0.1,0.6c0.1,0.1,0.2,0.1,0.3,0c0.2,0,0.4-0.2,0.5-0.4c0-0.3,0.1-0.5,0-0.7l0-0.1
		C4.8,48.6,4.9,48.7,4.9,48.9"/>
	<path class="st1" d="M4.3,53.2c-0.1,0-0.1-0.2-0.2-0.2c-0.1-0.1-0.3-0.4-0.5-0.3c-0.1,0.1-0.2,0.1-0.3,0.2c-0.3,0.4-0.7,0.6-1.1,1
		c-0.1,0-0.1,0-0.2-0.1c0.1-0.1,0.3-0.3,0.4-0.4c0.1-0.1,0.2-0.2,0.3-0.3C2.8,53,2.9,53,3,52.9c0.2-0.2,0.4-0.3,0.5-0.5
		c0.1-0.1,0.2-0.1,0.3,0c0.1,0.1,0.3,0.3,0.5,0.5S4.5,53.1,4.3,53.2"/>
	<path class="st2" d="M47.8,7.6c0.6,0,1.2,0.2,1.7,0.6c0.8,0.6,1.8,0.9,2.8,0.9c0.7,0,1.3,0.1,2,0.4c2.2,1,3.4,4.7,3.8,6.7
		c0.1,0.7,0.1,1.4,0,2.1c-0.1,0.6-0.1,1.3,0,1.9c0.2,1.1,0.6,1.1,0.9,1.1l0,0c0.3,0,0.6-0.1,0.9-0.1c0.3,0,0.5-0.1,0.8-0.1
		c0.6-0.1,1.2-0.1,1.7-0.2c0.8,0,1.6,0.1,2.4,0.3c0.8,0.2,1.6,0.3,2.4,0.3c0.6,0,1.2,0.1,1.9,0.2c1.4,0.2,2.8,0.6,4.1,1.1
		c0.7,0.3,1.3,0.7,1.8,1.3c0.4,0.4,0.9,0.8,1.5,1.1c3.1,1.7,4,3.6,3.1,6.8c-0.1,0.3-0.2,0.6-0.3,1c-0.3,1.1-0.6,2.2-1,3.2
		c-0.4,1-1,1.9-1.9,2.5c-0.2,0.2-0.5,0.4-0.7,0.6c-0.7,0.7-1.4,1.6-1.9,2.5c-0.7,1.4-1.8,2.6-3.1,3.4c-1,0.6-2.1,0.8-3.3,0.8h-0.2
		c-0.5,0.3,0,1.4,0.4,2c0.3,0.4,0.5,0.9,0.6,1.3c0.1,0.4,0.2,0.8,0.2,1.2c0,0.3,0,0.6,0.1,0.9c0.1,0.2,0.2,0.3,0.4,0.4
		c0.4,0.3,0.6,0.6,0.8,1.1c0.3,1.6,0.3,3.2-0.1,4.7c-0.2,0.3-0.4,0.6-0.6,0.8c-0.2,0.2-0.4,0.4-0.5,0.7c-0.1,0.3-0.2,0.5-0.2,0.8
		c-0.1,0.5-0.2,1-0.5,1.4c-1.2,1.8-3.9,2.7-6,3.4c-0.5,0.2-1,0.4-1.4,0.5c-0.5,0.2-1,0.3-1.5,0.3c-0.6,0-1.2-0.2-1.7-0.5
		c-0.3-0.1-0.5-0.3-0.9-0.4c-0.9-0.3-1.8-0.5-2.7-0.5c-0.5,0-1-0.1-1.5-0.1c-0.8-0.1-1.6-0.5-2.2-1l-0.3-0.2h-0.1
		c-0.7,0.1-1.4-0.2-2-0.6c-1.2,1.6-2.8,2.8-4.7,3.5c-1.4,0.7-2.8,1.2-4.3,1.4c-0.4,0.1-0.7,0.2-0.9,0.3c-1.2,0.4-2.5,0.6-3.8,0.6
		c-0.5,0-1,0-1.4-0.1c-1.5,0.1-2.9-0.3-4.1-1.2c-1.1-0.5-2.1-1.3-3-2.2c-1.3-0.8-2.4-1.9-3.2-3.2c-0.6-1-0.6-2.3,0.1-3.2
		c0.2-0.3,0.3-0.5,0.4-0.8l0.1-0.3c0.5-1,0.9-2.1,1.2-3.2c-1-0.3-1.9-1.2-2.7-2.7c-0.3-0.7-0.7-1.3-1.2-2c-0.2-0.2-0.4-0.5-0.7-0.8
		c-0.3-0.3-0.4-0.4-0.5-0.5c-0.9-0.8-2-1.5-3.1-2c-1-0.4-1.9-1-2.8-1.7c-0.6-0.5-1.1-1.2-1.5-1.9c-1.3-2-2.5-4.1-2.9-6.2
		c-0.3-1.6,0-3.2,0.8-4.5c0.2-0.4,0.4-0.9,0.7-1.3c0.1-0.2,0.2-0.4,0.3-0.6c0.1-0.3,0.2-0.6,0.3-0.8c0.2-0.6,0.6-1.2,1-1.7
		c0.7-0.7,1.5-1.3,2.4-1.8c0.9-0.5,2-0.8,3-0.9c0.3,0,0.6-0.1,0.9,0c0.2,0.1,0.4,0.1,0.6,0.1h0.1c0.2,0,0.4-0.1,0.6-0.2
		c0.3-0.1,0.7-0.2,1-0.2h0.1c1,0.1,4.4,1.8,5.3,1.8c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.2-0.1c0.4-0.4,0.8-0.9,1-1.5
		c0,0,0-0.1,0.1-0.2l0,0c0-0.1,0.1-0.2,0.1-0.3l0,0c0-0.1,0.1-0.2,0.1-0.4c0.2-0.7,0.3-1.4,0.5-2c0-0.2,0.1-0.4,0.1-0.6v-0.2l0-0.2
		v-0.2c0,0,0-0.1,0-0.1l0,0c0,0,0-0.1,0-0.1V20c0-0.1,0-0.1,0-0.2l0,0c0,0,0-0.1,0-0.1v0c0,0,0-0.1,0-0.2l0,0c0-0.1,0-0.1,0-0.2v0
		c0-0.1,0-0.1,0.1-0.2l0,0c0,0,0-0.1,0.1-0.2v0c0-0.1,0-0.1,0.1-0.2l0,0c0,0,0-0.1,0.1-0.2l0,0c0,0,0-0.1,0.1-0.1
		c0.5-0.8,0.8-1.7,1.1-2.6c0.5-1.8,1-3.6,2.5-4.6c0.7-0.3,1.4-0.5,2.1-0.5c0.6,0,1.1-0.1,1.7-0.4c0.4-0.2,0.8-0.5,1.2-0.8
		c0.5-0.5,1.2-0.9,1.9-1.2c0.1,0,0.2,0,0.2-0.1h0.1l0.3-0.1l0,0l0.2,0h0.1l0.2,0h0.1h0.2h0.1h0.2h0.1H41h0.7c0.2,0,0.5,0,0.8,0l0,0
		h0.3h0h0.3c0.2,0,0.5,0,0.7,0c0.7,0,1.3-0.1,2-0.3C46.6,7.7,47.2,7.7,47.8,7.6"/>
	<g>
		<defs>
			<path id="SVGID_1_" d="M46.8,8.1c-1.2,0.3-2.4,0.4-3.6,0.4c-1.3-0.2-2.6-0.1-3.9,0.2c-1.2,0.4-1.9,1.5-3,2s-2.6,0.2-3.8,0.9
				c-2.2,1.3-2.1,4.7-3.2,6.8c-1.2,2.3-0.7,6-2.9,7.7c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.3,0-0.4,0c-1,0-4-1.8-5.4-1.8
				c-0.7,0-1,0.3-1.7,0.4c-0.3,0-0.6,0-0.8,0c-0.3,0-0.6,0-0.8,0c-1,0.1-2,0.4-2.8,0.9c-0.9,0.4-1.6,1-2.3,1.7
				c-0.9,1-0.8,1.3-1.3,2.4C9.6,32.2,8.5,33.2,9,36c0.4,2.4,2.1,4.8,3.4,6.9c1.5,2.4,4.7,2.6,6.8,4.7c0.8,0.8,1.7,1.7,2.3,2.4
				c0.7,0.8,2.6,2.8,2.8,3.5s-1.1,3.2-1.4,3.9c-0.6,1.5-1.4,1.9-0.5,3.7c1.1,2.1,4.1,3.5,5.8,5c2.3,2,5.8,1.3,8.7,0.8
				s5.6-0.9,7.9-3.3c1.2-1.2,1.7-2.5,3.7-1.8c1.5,0.5,2.1,1.6,3.7,1.8c1.6,0.2,2.8,0.1,4.2,0.7c1.6,0.6,1.9,1.3,3.7,0.5
				C62.3,64,66,63,67.5,61c0.5-0.6,0.4-1.5,0.7-2.1c0.2-0.4,1-1.1,1.1-1.5c0.4-1.4,0.4-2.9,0.1-4.4c-0.2-0.7-1-1-1.1-1.4
				c-0.2-0.6,0-1.5-0.3-2.1c-0.6-1.7-2.1-3.2-1-3.9c0.2-0.1,2.4,0,3.6-0.8c2.3-1.4,3-4.1,4.9-5.9c1.1-1,2-1.4,2.6-3
				c0.4-1.3,0.8-2.7,1.3-4.1c0.9-3.2-0.2-4.8-2.9-6.3c-1.2-0.7-2-1.8-3.2-2.4c-1.3-0.5-2.7-0.8-4-1c-1.5-0.3-2.9-0.2-4.3-0.6
				c-1.3-0.3-2.7-0.3-4-0.1c-1.5,0.2-2.6,0.9-3.1-1.1c-0.3-1.3,0.2-2.8,0-4.1c-0.4-2-1.6-5.4-3.6-6.4c-1.2-0.6-2.5-0.2-3.8-0.9
				c-0.8-0.6-1.8-0.9-2.8-0.9L46.8,8.1"/>
		</defs>
		<clipPath id="SVGID_00000105389281421804730510000008610096553482924707_">
			<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000105389281421804730510000008610096553482924707_);">
			
				<radialGradient id="SVGID_00000092429580729618622200000008205704045897919405_" cx="-146.6941" cy="-188.6688" r="2.0909" gradientTransform="matrix(15.63 0 0 -15.63 2337.1423 -2912.2402)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFFFFF"/>
				<stop  offset="7.000000e-02" style="stop-color:#FFFFFF"/>
				<stop  offset="0.38" style="stop-color:#F2D4DA"/>
				<stop  offset="0.42" style="stop-color:#EFCBD1"/>
				<stop  offset="0.69" style="stop-color:#DB8D98"/>
				<stop  offset="0.9" style="stop-color:#CF6675"/>
				<stop  offset="1" style="stop-color:#CA5767"/>
			</radialGradient>
			
				<rect x="8.5" y="8.1" style="fill:url(#SVGID_00000092429580729618622200000008205704045897919405_);" width="71.7" height="60"/>
		</g>
	</g>
	<g>
		<defs>
			<path id="SVGID_00000044886542283401876480000008245151421723073972_" d="M47.4,8c-0.2,0-0.4,0-0.6,0.1c-1.2,0.3-2.4,0.4-3.5,0.4
				c-0.2,0-1.1-0.1-1.4-0.1c-0.8,0-1.7,0.1-2.5,0.3c-1.2,0.4-1.9,1.4-3,2s-2.6,0.2-3.8,0.9c-2.2,1.3-2.1,4.7-3.2,6.8
				c-0.9,1.8-0.8,4.2-1.7,6.1c0.1,0.7,0.2,1.3,0.4,2c0.3,1.1,2.4,1,2,3l0.1,0.3c1.7,5.4,11.3,4.1,14.1,2c1.4-1.1,4.1-2,5.2-3.3
				c0.9-1.2,4-2.2,4.8-3.4c1.2-2,2.8-4.7,2.6-6.5c-0.2-4.2-2.7-7.9-6.5-9.5c-1.3-0.6-1.7-1-2.8-1H47.4"/>
		</defs>
		<clipPath id="SVGID_00000082370815195734041980000018226558035176220057_">
			<use xlink:href="#SVGID_00000044886542283401876480000008245151421723073972_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000082370815195734041980000018226558035176220057_);">
			
				<radialGradient id="SVGID_00000145053047511683972320000005210844071512471432_" cx="-144.5468" cy="-185.7306" r="2.0915" gradientTransform="matrix(12.01 0 0 -12.01 1777.2224 -2217.4399)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#CA5767"/>
				<stop  offset="0.21" style="stop-color:#CA5767"/>
				<stop  offset="0.31" style="stop-color:#CC5D6C"/>
				<stop  offset="0.43" style="stop-color:#D16C7B"/>
				<stop  offset="0.58" style="stop-color:#D98793"/>
				<stop  offset="0.73" style="stop-color:#E5ABB4"/>
				<stop  offset="0.87" style="stop-color:#F2D4DA"/>
				<stop  offset="1" style="stop-color:#F2D4DA"/>
			</radialGradient>
			
				<rect x="27.8" y="8" style="fill:url(#SVGID_00000145053047511683972320000005210844071512471432_);" width="29.2" height="27.2"/>
		</g>
	</g>
	<g>
		<defs>
			<path id="SVGID_00000099663325205885392990000001994038127140201393_" d="M48.4,8.7l-0.6,0.1c-1.2,0.3-2.4,0.5-3.7,0.4
				c-0.2,0-1.1-0.1-1.4-0.1c-0.9,0-1.7,0.1-2.6,0.3c-1.3,0.4-2,1.5-3.1,2s-2.7,0.2-3.9,0.9c-1.2,0.7-2.7,3.4-3.1,6.7
				c-0.1,1.1,0.6,2.2,0.5,3.4c-0.6,5.4,0.9,4.2,2,6.4c2.2,4.1,2,5.4,0.9,5.1c1.2,1.5,2.3,3.3,1.4,4.5c3.7,0.8,8.4-0.2,10.2-1.6
				c1.5-1.1,3.5-2.2,4.6-3.7c1-1.2,2.8-5.2,3.6-6.5c1.3-2.1,2.6-2.5,3.6-6.3c1.7-6.4-1.6-9.1-5.1-10.6c-1-0.5-2.1-0.9-3.2-1.1
				L48.4,8.7"/>
		</defs>
		<clipPath id="SVGID_00000029763403438517969050000000469623964165041810_">
			<use xlink:href="#SVGID_00000099663325205885392990000001994038127140201393_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000029763403438517969050000000469623964165041810_);">
			
				<radialGradient id="SVGID_00000160896617424321022780000005467694209668358591_" cx="-145.4149" cy="-186.4276" r="2.0915" gradientTransform="matrix(13.38 0 0 -12.74 1989.9989 -2356.8198)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#CA5767"/>
				<stop  offset="0.11" style="stop-color:#CB5B6B"/>
				<stop  offset="0.22" style="stop-color:#D06877"/>
				<stop  offset="0.34" style="stop-color:#D77E8B"/>
				<stop  offset="0.47" style="stop-color:#E09CA7"/>
				<stop  offset="0.59" style="stop-color:#EDC3CA"/>
				<stop  offset="0.64" style="stop-color:#F2D4DA"/>
				<stop  offset="0.75" style="stop-color:#F4DBE0"/>
				<stop  offset="0.91" style="stop-color:#FAF0F2"/>
				<stop  offset="1" style="stop-color:#FFFFFF"/>
			</radialGradient>
			
				<rect x="29.7" y="8.7" style="fill:url(#SVGID_00000160896617424321022780000005467694209668358591_);" width="28.7" height="30.6"/>
		</g>
	</g>
	<g>
		<defs>
			<path id="SVGID_00000019676046852101417840000017143712344372586627_" d="M42.9,42.8c-2.3,0.6-6.4,0-8.4,1.4
				c-1.8,1.2-3.3,2.9-4.3,4.8c-2.1,2.3-4.3,4.3-5.6,6c-1.4,2-1.6,4.6-0.8,6.8c2.5,5,8.8,6.6,13.6,5.1c4.7-1,10.4-2.4,11-8.6
				c0.3-3.1,1.2-6.2,0.2-9.1c-0.8-2.1-0.9-6.7-4.6-6.7C43.7,42.6,43.3,42.7,42.9,42.8"/>
		</defs>
		<clipPath id="SVGID_00000131367070300557217140000009121785070320685724_">
			<use xlink:href="#SVGID_00000019676046852101417840000017143712344372586627_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000131367070300557217140000009121785070320685724_);">
			
				<radialGradient id="SVGID_00000067914392238803635480000013321378359334192048_" cx="-141.3228" cy="-184.5724" r="2.0915" gradientTransform="matrix(8.69 0 0 -8.69 1261.8368 -1558.2001)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFFFFF"/>
				<stop  offset="0.41" style="stop-color:#F2D4DA"/>
				<stop  offset="0.45" style="stop-color:#EFCBD1"/>
				<stop  offset="0.71" style="stop-color:#DB8D98"/>
				<stop  offset="0.9" style="stop-color:#CF6675"/>
				<stop  offset="1" style="stop-color:#CA5767"/>
			</radialGradient>
			
				<rect x="23" y="42.6" style="fill:url(#SVGID_00000067914392238803635480000013321378359334192048_);" width="26.7" height="25.8"/>
		</g>
	</g>
	<g>
		<defs>
			<path id="SVGID_00000053545616700680958900000014032663957196921786_" d="M8.9,35.2c0,2.2,1.8,4.7,2.7,6.6
				c1.4,2.8,2.7,2.9,5.3,4.3c2,1,3.7,2.7,4.7,4.7c3.1,6,6.5,0.4,9.1-1.8c2.1-2,3.1-4.9,2.6-7.8c-0.5-2-1.7-3.9-3.2-5.3l0.9,0.6
				c-1.2-1.1-0.3-2.5-0.8-3.9c-0.4-1.2-1.2-2.7-1.8-4c-1-2-2.1-1.5-3-1C25,27.9,24.5,28.1,24,28c-1.5-0.3-2.9-0.7-4.3-1.2
				c-0.8-0.3-1.6-0.5-2.5-0.5C12.9,26.3,9,30.5,8.9,35.2"/>
		</defs>
		<clipPath id="SVGID_00000013913130021565747320000009764893590263817126_">
			<use xlink:href="#SVGID_00000053545616700680958900000014032663957196921786_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000013913130021565747320000009764893590263817126_);">
			
				<radialGradient id="SVGID_00000065779078870766374210000002545567173470834342_" cx="-144.0782" cy="-186.3932" r="2.0915" gradientTransform="matrix(11.06 0 0 -11.06 1627.0482 -2018.4698)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFFFFF"/>
				<stop  offset="7.000000e-02" style="stop-color:#FFFFFF"/>
				<stop  offset="0.43" style="stop-color:#F2D4DA"/>
				<stop  offset="0.46" style="stop-color:#EFCBD1"/>
				<stop  offset="0.72" style="stop-color:#DB8D98"/>
				<stop  offset="0.9" style="stop-color:#CF6675"/>
				<stop  offset="1" style="stop-color:#CA5767"/>
			</radialGradient>
			
				<rect x="8.9" y="26.4" style="fill:url(#SVGID_00000065779078870766374210000002545567173470834342_);" width="25.1" height="30.4"/>
		</g>
	</g>
	<g>
		<defs>
			<path id="SVGID_00000118375757782985466480000008610709850991684518_" d="M59.4,22.2c-1.3,0.4-2.5,1-3.6,1.8
				c-1,0.8-1.7,2.8-2.7,3.4c-2.4,1.1-2.4,1.7-3.7,3.7c-1.1,1.7-2.4,4.1-3.5,5.9c0,0.7,0,1.4,0.2,2.1c0.2,1.2,1.5,3,1.5,4l0-0.4
				c1.1,5.9,13.1,7.7,10.9,4.8c-5.7-7.4,3.2-11,4.3-9.7c7,8.7,9.8,3.6,10.4,2.7c1.4-1.7,2.4-3.7,2.9-5.8c2.2-12.3-4.7-10.6-8.6-12.3
				c-0.2,0-0.4,0-0.6,0c-1.2,0.2-2.5,0.2-3.7-0.1l-0.7-0.2c-0.2,0-0.5-0.1-0.7-0.1c-0.5-0.1-0.9-0.1-1.4-0.1
				C60.2,22.1,59.8,22.1,59.4,22.2"/>
		</defs>
		<clipPath id="SVGID_00000155106026381334813780000005881127974864428687_">
			<use xlink:href="#SVGID_00000118375757782985466480000008610709850991684518_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000155106026381334813780000005881127974864428687_);">
			
				<radialGradient id="SVGID_00000179645518773972947070000014725712543440735109_" cx="-147.8731" cy="-190.0017" r="2.0915" gradientTransform="matrix(22.19 0 0 -22.19 3369.7983 -4194.1299)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#CA5767"/>
				<stop  offset="0.31" style="stop-color:#CA5767"/>
				<stop  offset="0.37" style="stop-color:#CD606F"/>
				<stop  offset="0.48" style="stop-color:#D57886"/>
				<stop  offset="0.61" style="stop-color:#E1A0AA"/>
				<stop  offset="0.76" style="stop-color:#F2D4DA"/>
				<stop  offset="1" style="stop-color:#F2D4DA"/>
			</radialGradient>
			
				<rect x="46" y="22.1" style="fill:url(#SVGID_00000179645518773972947070000014725712543440735109_);" width="32.5" height="28.4"/>
		</g>
	</g>
	<g>
		<defs>
			<path id="SVGID_00000107563566759160099780000013464756643112199045_" d="M58.2,46.7C58.2,46.7,58.2,46.7,58.2,46.7L58.2,46.7
				C58.2,46.7,58.2,46.7,58.2,46.7 M55.8,24c-0.3,0.3-0.6,0.7-0.9,1.1c-0.4,0.8-1,1.4-1.7,1.9c-0.6,0.3-1.2,0.6-1.7,1.1
				c-0.4,0.5-0.9,1.1-1.2,1.7c-0.8,1.1-2.3,1.4-3.7,1.4c-0.3,0-0.7,0-1,0c-1.9,1.2-3.9,1.9-5.7,1.5c-1.1-0.3-2.5-2.3-3.6-2.1
				c-1.1,0.2,0.6,0.7-0.2,1.7c-0.8,1-2.1,0.2-2.9-0.7c-0.5-0.6-0.9-1.1-1-1.1c-0.2,0.1,0,0.7,0,0.9c0,0.6-0.6,0.9-0.6,1.5
				c0,0.4,0,0.8,0.1,1.2c0.2,0.5,0.2,0.9,0.2,1.4c0,1,0,0.7-0.1,1.8c-0.1,2.1-0.9,2.9-1.5,5c-0.5,2.2,1.8,3.4-1,7.6
				c-0.9,1.4-5.1,3.3-4.8,5c1.2-0.2,1.8-1.5,2.7-2.4c2-2,7.5-4.1,10.6-4.3c3.3-0.1,0.1,3.6,1.1,4.3c0.9,0.7-0.9,2.6,0.1,2.2
				c1-0.5,2.5-5.4,3.1-6.5c0.4-0.8,5.7-13,5.8,5.4c0,1.8,1.1,2.6,0.6,4.3c-0.2,0.8-3.3,5.2,1.8,4.7c0.7-3.9-2.2-6.6-0.7-8.5
				c0.8-1,3.6-1.2,3.6-2.5c-0.1-2.9,1.2-2.8,2.4-2.7c0.8,0,1.5,0.1,1.8-0.6c0.2-0.5,0.5-1,0.8-1.5c0,0-0.1-0.1-0.1-0.4
				c-0.1-1.3,1-1.3,0.5-2.5c1.2,0.3,1.5-0.9,2.5-1.2c0.9-0.1,1.8-0.1,2.8,0c1.7,0.3,2.5,3,2.8,2.8c0.4-0.6-0.5-2-1.4-2.8
				c-0.8-0.9-1.5-1.8-2.1-2.8c-0.8-1.1-0.3-2.6-1.6-3.2c-0.3-0.2-0.7-0.2-1.1-0.1c-0.9,0.2-1.8,0-2.5-0.6c-6.6,4.7-1-2.4-1.4-4.1
				C55.7,29.5,54,28.9,55.8,24 M55.8,23.9C55.8,23.9,55.8,23.9,55.8,23.9"/>
		</defs>
		<clipPath id="SVGID_00000139271818656739743790000015762418147716224939_">
			<use xlink:href="#SVGID_00000107563566759160099780000013464756643112199045_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000139271818656739743790000015762418147716224939_);">
			
				<radialGradient id="SVGID_00000067209754806962418680000012164801805034040469_" cx="-143.9694" cy="-186.7859" r="2.0915" gradientTransform="matrix(11.56 0 0 -11.56 1709.9158 -2115.0098)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFFFFF"/>
				<stop  offset="9.000000e-02" style="stop-color:#FFFFFF"/>
				<stop  offset="0.37" style="stop-color:#F2D4DA"/>
				<stop  offset="0.4" style="stop-color:#EEC8CF"/>
				<stop  offset="0.5" style="stop-color:#E1A0AA"/>
				<stop  offset="0.59" style="stop-color:#D7808D"/>
				<stop  offset="0.69" style="stop-color:#D06978"/>
				<stop  offset="0.78" style="stop-color:#CB5C6B"/>
				<stop  offset="0.85" style="stop-color:#CA5767"/>
				<stop  offset="1" style="stop-color:#CA5767"/>
			</radialGradient>
			
				<rect x="24.3" y="24" style="fill:url(#SVGID_00000067209754806962418680000012164801805034040469_);" width="42.7" height="38.9"/>
		</g>
	</g>
	<path class="st17" d="M34.9,47.6c-0.7-0.5,0-2.3,0.8-3.1c0.8-0.9,2.1-2.3,1.8-2.5c-0.3-0.2-1.8,0.2-2.2,0c-0.4-0.2,2.5-1.3,2.8-1.5
		c0.3-0.2-2.1-0.8-3-0.9s-2.7,0.2-2.5-0.2s1.2-0.9,1.4-1.1c0.2-0.2-1.4-1-1.5-1.4s0.3-0.8,2-0.4c1.2,0.2,2.5,0.3,3.7,0.4
		c0.5,0.1,0.9,0.3,1.4,0.5c0.3,0.2,0.5-0.4,0.7-0.3c0.2,0.1,0.7,0.4,0.8,0.1c0-0.5-0.3-1-0.7-1.4c-0.5-0.4-1-0.9-1.4-1.5
		c-0.1-0.3-0.4-1,0-1.2c0.4-0.2,0.8,0,1-0.1s0.6-0.7,1.2-0.4c0.4,0.2,0.7,0.4,1,0.6c0.2,0.2,0.5,0.2,0.8,0.1
		c0.3-0.1,0.7-0.6,1.2-0.3c0.5,0.3,1.4,1.1,2.3,0.9c0.7-0.1,1.2-0.5,1.6-1.1c0.4-0.8,0.8-1.6,1.2-2.4c0.3-0.3,1-0.9,0.9-0.6
		c-0.4,0.8-0.7,1.8-0.8,2.7c0,0.8-0.1,1,0.1,1.3c0.3,0.2,0.6,0.2,0.9,0c0.3-0.1,0.8-0.5,0.8-0.3c0,0.2-1.7,1.4-2.2,1.9
		c-0.5,0.5-1,1.5-0.8,1.9c0.3,0.4,1.2,0.2,1.7,0.2s1.6,0.1,1.4,0.3c-0.1,0.2-1.9,1.4-1.9,1.7c0,0.2,1.1,0.6,1.7,0.6
		c0.9,0.1,1.8,0.2,2.6,0.4c0.6,0.1,1,0.1,1.2,0.4s0.3,0.6,0.1,0.6c-0.6,0-1.2-0.1-1.8-0.3c-1-0.3-2-0.5-3-0.6c-0.5,0-1.9,0-2.1,0.4
		c-0.2,0.4,0.8,0.8,1,1c0.2,0.2,1.1,1.1,0.5,1.2c-0.5,0.1-0.9-0.3-1.1-0.4c-0.4-0.2-0.8-0.5-1.2-0.8c-0.2-0.2-1.1-1.1-1.5-1.1
		c-0.6,0.1-1.2,0.2-1.8,0.5c-0.5,0.4-0.9,0.8-1.3,1.3c-0.3,0.3-1.5,1.1-2,1.5c-0.5,0.4-3.2,2.6-3.7,2.9C36.7,47.5,35.4,48,34.9,47.6
		"/>
	<path class="st18" d="M42.7,31.4c-0.2-0.5-0.1-1,0-1.4c0.1-0.4,1.3-2.5,2.5-2.1c0.5,0.1,0.8,0.4,1,0.9c0.2,0.8,0.3,2.2-0.4,2.8
		C45,32.3,43.6,32.3,42.7,31.4C42.8,31.4,42.7,31.4,42.7,31.4"/>
	<path class="st18" d="M37.1,34c0.4-0.1,0.7-0.4,0.8-0.8c0.1-0.4,1-0.5,1-0.7c0-0.2-0.9-2.6-1.4-2.8c-0.5-0.2-0.8,0.2-1.4,0.1
		c-0.4,0-0.8-0.2-1.1,0.3c-0.4,0.7-1.1,1.7-0.6,2.5C35,33.4,35.9,34.2,37.1,34"/>
	<path class="st18" d="M50.6,35.8c-0.2,0.2-0.5,0.3-0.7,0.1c0,0,0,0,0,0c0.6-0.7,1.4-1.3,2.2-1.8c0.3-0.3,0.1-1.8,0.2-2.2
		c0.2-0.5,1.7-1.5,2.3-1.1c0.3,0.1,0.6,0.3,0.8,0.6c0.3,0.6,0.4,1.2,0.3,1.9c-0.1,0.5-0.2,1.1-0.5,1.6c-0.2,0.5-1.1,0.5-1.6,0.5
		c-0.6,0.1-1.1-0.4-1.7,0C51.6,35.3,51.1,35.6,50.6,35.8"/>
	<path class="st18" d="M44.1,37.9c0.2-0.4,0-0.9,0-1.3s0.2-0.8-0.2-1c-0.2,0.7-0.2,1.5,0,2.2C43.9,38,44,37.9,44.1,37.9"/>
	<path class="st18" d="M30.7,39.8c0.6-0.9,1.5-1.5,2.6-1.7c0-0.1-0.3-0.2-0.4-0.2c-0.7-0.1-1.3-0.5-1.7-1.1c-0.8-1-1.4-1.9-2.8-1.4
		c-1.4,0.4-1.5,2.9-1.1,4.1c0.4,1,1.4,1.5,2.4,1.1C30.2,40.4,30.5,40.2,30.7,39.8"/>
	<path class="st18" d="M60.1,38c1.5-0.6,2.8,0.2,2,1.4c-0.4,0.7-0.3,1.4-0.7,2.1c-0.4,0.6-1.2,0.8-1.8,0.6c-0.7-0.2-1.3-0.5-1.8-1
		c-0.5-0.5-0.6-0.9-1.4-1.1c-1.3-0.3-2.6,0-3.8-0.5c1.4-0.7,3,0.1,4.4,0C57.7,39.5,58.1,38.4,60.1,38"/>
	<path class="st18" d="M52.6,39.5c-0.3-0.2-0.7-0.2-1.1,0C51.8,39.6,52.2,39.6,52.6,39.5"/>
	<path class="st18" d="M33.7,43.9c-1.4-1.7-4.3,1.5-4.6,1.7c-0.2,0.2-0.8,0.6-0.8,0.8c-0.2,0.7,1.3,1.5,1.8,1.8
		c0.6,0.4,1.2,1.3,2,0.6c0.9-0.7,0.9-1.2,1.1-2.2c0.2-0.9,0.6-1.7,1.2-2.4c0.7-0.5,1.4-1.2,2.1-1.8C35.3,42.4,34.7,43.5,33.7,43.9"
		/>
	<path class="st18" d="M54.6,36.7c-0.3-1.1-2.1,0-2.6,0c0.5,0.1,1.3,0.1,2,0.2C54.2,36.9,54.6,37,54.6,36.7"/>
	<path class="st18" d="M46.4,42.9c0.5,0.3,0.7,1,1.3,1.4c0.6,0.3,1.1,0.6,1.6,1c0.2,0.2,0.5,1.1,0.1,1.3c-0.6,0.3-0.8-0.9-1.1,0.1
		c-0.3,1-1.5,1.4-1.7,2.5c-0.1,0.7-0.7,1.7-0.2,2.4c0.1,0.2,0.4,0.3,0.6,0.3c0.4,0,0.8,0.1,1.1,0.3c0.5,0.4,1.1,0.5,1.7,0.2
		c0.6-0.2,1.1-0.8,1.3-1.5c-0.2-0.8-0.3-1.5-0.3-2.3c0-0.8,0.4-1.3,0-2c-0.3-0.5-0.7-1-1.2-1.4c-0.4-0.3-0.6-0.9-0.9-1.3
		c-0.5-0.7-1.1-1.3-1.7-1.9c-0.3-0.4-0.7-0.6-1.2-0.6c-0.7,0.1-1.3,0.5-1.7,1.1c-0.5,0.5-1.1,1-1.7,1.5c-0.6,0.5-0.4,1.4-0.7,2.1
		c-0.3,0.6-0.6,1.2-0.8,1.9c0.4-0.2,0.7-0.8,0.9-1.2c0.2-0.3,0.4-0.5,0.6-0.8c0.2-0.3,0.5-0.3,0.8-0.5c0.7-0.7,0.7-1.5,1.2-2.2
		C44.8,42.7,45.7,42.6,46.4,42.9"/>
	<path class="st18" d="M59.2,49.5c-0.1-0.4-0.1-0.8-0.1-1.3c0.1-0.5,0-1-0.3-1.4c-0.3-0.6-1.2-1.9-2-1.5c-0.3,0.1-0.5,0.5-0.9,0.3
		s-0.5-0.6-1-0.8c-0.5-0.2-0.9-0.2-1.4-0.3c-0.3-0.1-0.6-0.2-1-0.2c0.3,0.6,1.4,1,1.9,1.5c1.1,0.9,0.6,2,0.5,3.2
		c-0.1,0.4-0.1,0.8,0.1,1.2c0.2,0.4,1.2,0.4,1.4,0.6c0.6,0.3,1.2,0.3,1.8,0.2C58.8,50.6,59.1,50.1,59.2,49.5"/>
	<path class="st18" d="M46.6,60.5c0.3-0.1,0.8,0.3,1.5,0.6c0.6,0.3,1.3,0.4,2,0.5c0.8,0.1,1.4,0.5,1.9,1.1c0.3,0.7,0.3,1.4,0,2.1
		c-0.6,0.8-0.8,1.8-0.6,2.8c0.3,1.4,0.9,1.9-0.1,1.8c-0.8-0.1-1.6-0.3-2.3-0.6c-0.8-0.1-1.5-0.5-2-1c-0.6-0.8-1-0.6-2.1-1.3
		c-1-0.6-1.7-2-1.1-2.5c0.5-0.5,2.1-2,2.3-2.3S46.5,60.5,46.6,60.5"/>
	<path class="st18" d="M67.1,52.3c0-0.3,0.7-0.8,0.8-1c0.1-0.7,0-1.3-0.1-2c-0.1-0.5-0.2-1.1,0.2-1.3c0.6-0.4,1.4-0.5,2.1-0.3
		c0.6,0.3,1.2,0.5,1.7,0.7c0.6,0.2,2.1,1,2.4,1.6c0.3,0.5,0.6,1.2,0.2,1.8c-0.4,0.6-0.4,1-0.7,1.3c-0.5,0.4-1.1,0.9-1.5,1.4
		c-0.4,0.5-0.7,1.7-1.3,1.9c-0.5,0.3-1.6,0.6-1.9,0.2c-0.3-0.4-0.4-1-0.4-1.5c0.1-0.3,0.8-0.9,0.6-1.4S68.6,53.2,68,53
		C67.4,52.8,67.1,52.7,67.1,52.3"/>
	<path class="st18" d="M18.9,57c-0.3-0.3-0.6-0.6-0.4-0.8c0.3-0.5,0.5-1,0.6-1.5c0.1-0.7,0.1-1.4,0-2c0-0.4,0.1-0.8,0.3-1.2
		c0.1-0.3,0-0.9,0.6-1.1c0.6-0.2,1.2-0.2,1.8,0c0.5,0.1,1.6,0.4,1.9,0.9c0.3,0.6,0.5,1.2,0.8,1.5c0.3,0.2,1.4,0.7,1.4,0.8
		c0,0.1-0.2,0.4-0.6,1c-0.3,0.9-0.4,1.7-0.4,2.6c0,0.3,0,1.5-0.4,1.8c-0.4,0.3-1.1,0-1.7-0.3c-0.6-0.3-1.2-0.6-1.8-0.9
		c-0.3-0.3-0.3-0.6-0.5-0.7C20.3,56.9,19.3,57.2,18.9,57"/>
	<path class="st18" d="M41.3,50.9c0.2,0.4,0.3,0.8,0.3,1.3c0,0.5-0.1,1-0.3,1.4C41,54,40.5,54.3,40,54.5c-0.2,0.1-1,0.3-1.4,0
		c-0.4-0.3-0.7-0.6-1-1c-0.4-0.9-0.2-1.9,0.3-2.7c0.4-0.4,1-0.4,1.4-0.8c0.4-0.3,0.7-0.7,1.1-0.9c0,0.4-0.3,0.9,0,1.3
		C40.8,50.7,41.1,50.6,41.3,50.9"/>
	<path class="st18" d="M50.8,41.6c-0.4-0.1-0.9-0.2-1.3-0.2c0.3,0.5,1.2,0.4,1.7,0.6c0.2,0.1,0.4,0.2,0.6,0.4
		c0.6,0.3,1.7,0.7,1.8,0.9c0.5,0.4,1.1,0.5,1.7,0.4c0.2-0.1,0.7-0.2,0.5-0.5c-0.4-0.9-1.7-0.6-2.4-1c-0.2-0.1-0.2-0.3-0.6-0.4
		c-0.3,0-0.6,0-1-0.1C51.5,41.6,51.1,41.6,50.8,41.6"/>
	<path class="st18" d="M44.6,33.4c0,0.2,0,0.4-0.1,0.5c-0.2,0.6-0.2,1.3,0.1,2c0.2-0.2,0.2-0.5,0.1-0.8c0-0.3,0-0.6,0.1-0.9
		C44.8,33.9,44.7,33.6,44.6,33.4"/>
	<path class="st18" d="M42.3,40.5c0.2,0.3-0.1,0.7-0.3,1c-0.2,0.3-0.5,0.4-1.2,1.1s-1.4,1.5-1.6,1.8c-0.2,0.2-0.6,0.9-0.8,0.8
		c-0.2-0.1,0.3-1.1,0.5-1.3c0.3-0.5,0.7-0.9,1.1-1.3c0.6-0.4,1.1-0.8,1.5-1.4c0-0.1-0.5-0.1-0.7-0.1c-0.2,0-1.2,0.4-1.4,0.5
		c-0.3,0.1-0.6-0.1-0.2-0.4c0.2-0.2,0.4-0.4,0.7-0.4c0.2,0,0.8-0.1,0.9-0.2c0.1-0.1,0.1-0.5,0-0.6c-0.1-0.1-0.3,0-0.5,0
		s-2.1-0.3-2.4-0.4c-0.3-0.1-2.2-0.6-2.5-0.6c-0.3-0.1-0.4-0.3,0.2-0.3s1.5,0.2,1.9,0.2c0.4,0,0.8-0.1,1.1,0
		c0.6,0.1,1.1,0.1,1.7,0.1c0.3-0.1,0.7-0.3,0.8-0.5c0.1-0.2,0.1-0.4-0.1-0.6c-0.5-0.3-0.9-0.7-1.2-1.2c-0.1-0.4-0.4-0.7-0.7-1
		c-0.2-0.1-0.6-0.2-0.8-0.4s-0.2-0.8,0-0.7c0.4,0.2,0.8,0.4,1.2,0.7c0.1,0.2,0.3,0.6,0.6,0.8c0.3,0.3,0.7,0.7,1,0.9
		c0.4,0.2,0.7,0.5,0.9,0.8c0.8,0,0.9,0,1.5,0c0.5,0,1,0.1,1.5,0.2c0.2,0.1,0.7,0.3,0.9,0.2s0.7-1.3,0.9-1.5s1.2-1.4,1.3-1.8
		c0.4-0.5,0.9-0.8,1.4-1.1c0.1,0.1,0.3,0.2-0.1,0.6c-0.6,0.6-1.2,1.2-1.8,1.8c-0.2,0.3-0.8,1.4-0.9,1.6c-0.1,0.2-0.5,0.5-0.4,0.6
		c0.1,0.1,0.8,0.3,0.9,0.5c0.1,0.1,0.6,0.9,0.3,1.1c-0.3,0.1-0.7,0-0.9-0.3c-0.2-0.3-0.4-0.5-0.7-0.8c-1-0.5-2.1-0.8-3.2-0.6
		c-0.3,0.1-1,0.3-1,0.7C41.6,39.5,41.8,40.1,42.3,40.5"/>
	<path class="st18" d="M43.2,37.2c-0.2,0-0.8-0.6-1-1c-0.2-0.5-0.5-0.9-0.8-1.4c-0.1-0.2-0.2-0.6-0.4-0.7s-0.8-0.9-0.6-1
		c0.2-0.1,0.8,0.6,0.9,0.8s0.7,1.1,0.9,1.3c0.2,0.2,0.7,1,0.8,1.2S43.3,37.2,43.2,37.2"/>
</g>
</svg>
