<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 114.31 80" style="enable-background:new 0 0 114.31 80;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:url(#SVGID_00000023247515582527644530000008431095009030950543_);}
	.st2{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
</style>
<g id="Layer_2">
</g>
<g id="Layer_1">
	<g>
		
			<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="6.7994" y1="79.9503" x2="8.2284" y2="79.9503" gradientTransform="matrix(51.6491 23.9658 23.9658 -51.6491 -2246.4172 3977.1821)">
			<stop  offset="0" style="stop-color:#E96E85"/>
			<stop  offset="0.02" style="stop-color:#E5566C"/>
			<stop  offset="0.05" style="stop-color:#E24256"/>
			<stop  offset="0.08" style="stop-color:#E03245"/>
			<stop  offset="0.12" style="stop-color:#DE263A"/>
			<stop  offset="0.18" style="stop-color:#DD2033"/>
			<stop  offset="0.34" style="stop-color:#DD1E31"/>
			<stop  offset="0.52" style="stop-color:#DA1E30"/>
			<stop  offset="0.65" style="stop-color:#D11D2F"/>
			<stop  offset="0.76" style="stop-color:#C11B2C"/>
			<stop  offset="0.87" style="stop-color:#AB1928"/>
			<stop  offset="0.97" style="stop-color:#8E1622"/>
			<stop  offset="1" style="stop-color:#821520"/>
		</linearGradient>
		<path class="st0" d="M114.31,0H66.74C52.01,0,48.42,2.42,42.55,8.82L0,55.25c33.98-1.59,44.46,32.46,78.96,23.13L114.31,0z"/>
		
			<linearGradient id="SVGID_00000150093246222994506620000013794208553958117823_" gradientUnits="userSpaceOnUse" x1="22.8317" y1="84.7814" x2="24.2603" y2="84.7814" gradientTransform="matrix(-37.8254 -34.4895 -34.4895 37.8254 3882.0894 -2357.823)">
			<stop  offset="0" style="stop-color:#C52032"/>
			<stop  offset="0.84" style="stop-color:#C52032"/>
			<stop  offset="0.87" style="stop-color:#C62335"/>
			<stop  offset="0.89" style="stop-color:#C82D3E"/>
			<stop  offset="0.91" style="stop-color:#CD3D4D"/>
			<stop  offset="0.93" style="stop-color:#D35462"/>
			<stop  offset="0.94" style="stop-color:#DA727D"/>
			<stop  offset="0.96" style="stop-color:#E4969F"/>
			<stop  offset="0.98" style="stop-color:#EFC2C7"/>
			<stop  offset="0.99" style="stop-color:#FCF3F4"/>
			<stop  offset="1" style="stop-color:#FFFFFF"/>
		</linearGradient>
		<path style="fill:url(#SVGID_00000150093246222994506620000013794208553958117823_);" d="M78.96,78.38
			C44.84,87.61,34.22,54.41,1.12,55.21L43.14,9.36c5.7-6.22,9.21-8.57,23.61-8.57h47.21L78.96,78.38l-0.03,0L78.96,78.38z"/>
		<path class="st2" d="M51.95,18.12c0,0,2.12-3.89,3.79-5.17c0,0,1.83-1.1,1.88-2.91c0,0,0.14-1.22-0.85-1.97
			c0,0-2.58-2.1-4.67,1.47c0,0-9.87,18.59-13.27,37.32c0,0-0.26,0.61-0.33-1.1l-1.51-16.29l-0.02-0.2
			c-0.05-0.64-0.14-1.79-1.32-1.88c0,0-1.56-0.29-2.47,2.04L28.8,40.17c0,0-0.72,1.35,0.44,1.86c0,0,1.3,0.73,2.15-1.08l2.02-4.32
			c0.4-0.88,0.7-0.76,0.7-0.76c0.23,0.05,0.33,0.93,0.33,0.93l1.8,21.27c0.02,0.16,0.18,1.86,1.61,1.86
			c1.04-0.01,1.36-1.16,1.57-2.12l0.13-0.62c0.52-2.53,2.35-11.55,5.21-21.2C46.86,28.89,49.57,21.8,51.95,18.12L51.95,18.12z
			 M49.04,36.53c-2.04,0.46-2.3,2.77-2.3,2.77l-1.25,9.19c-0.2,1.83,2.18,0.5,2.18,0.5c0.98-0.57,1.15-1.78,1.15-1.78l0.38-2.3
			l1.16-6.22C50.73,36.08,49.04,36.53,49.04,36.53L49.04,36.53z M52.33,43.2c0.11-1.06,0.78-5.85,0.78-5.85
			c0.22-2.99,1.84-3.06,1.84-3.06l1.51-0.19c0,0,0.53-0.05,1.52-0.5c1.3-0.56,1.71,0.5,1.71,0.5c0.67,1.36-1.53,2.34-1.53,2.34
			c-2.05,1.1-2.16,3.67-2.16,3.67l-0.35,3.47c-0.28,2.45-2.03,2.94-2.03,2.94C51.44,47.25,52.33,43.2,52.33,43.2L52.33,43.2z
			 M51.41,31.86c1.34-1.07,0.94-1.95,0.94-1.95c-0.5-1.44-2.32,0.1-2.32,0.1c-1.53,1.09-1,2.16-1,2.16
			C49.71,33.3,51.41,31.86,51.41,31.86L51.41,31.86z M75.77,36.21c-0.31,1.97-2.05,2.34-2.05,2.34c-1.6,0.41-1.25-1.15-1.25-1.15
			l0.24-1.47l1.3-7.22c0.3-1.64,1.97-1.96,1.97-1.96c1.36-0.3,0.91,1.63,0.87,1.88C76.8,28.87,75.77,36.21,75.77,36.21z M77.88,23.2
			c0,0,1.46-1.2,0.79-2.08c0,0-0.62-0.92-2.34,0.3c0,0-1.55,1.26-0.6,2.15C75.73,23.56,76.63,24.4,77.88,23.2z M93.3,31.82
			c1.58-0.68,2.47-0.27,2.81,0.32c0.36,0.62,0.25,1.23-0.04,1.66c-0.5,0.73-2.2,1.3-3.52,1.69c-0.99,0.29-1.76,0.58-1.76,0.58
			c-2.33,0.74-5.07,1.82-5.07,1.82c-6.33,2.67-11.99,5.6-16.93,8.49c-0.07,0.71-0.12,1.28-0.13,1.69c0,0-0.9,10.41-1.02,11.94
			c0,0-0.04,1.9,0.23,3.05l0.08,0.34c0.31,1.24,0.83,3.34-0.62,4.26c-1.3,0.83-2.45-0.14-2.7-0.46c-0.18-0.22-0.65-0.81-0.29-3.73
			c0,0,0.78-7.47,0.96-9.89l0.41-5.36c-12.88,7.96-19.85,15.08-19.85,15.08c-0.71,0.65-2.42,2.11-3.66,0.51
			c-1.17-1.49,1.96-3.78,1.96-3.78c6.8-5.75,14.69-10.84,21.79-14.92l0.12-1.66c0,0,0.08-0.53-0.48-0.15c0,0-2.39,1.4-4.14,0.59
			c0,0-1.4-0.61-1.35-2.68c0,0-0.01-6.52,2.74-9.24c0,0,1.58-1.64,3.12-1.58c0.93,0.04,0.81,1.39-0.42,2.43
			c-1.05,0.89-1.33,1.97-1.66,3.25l-0.19,0.73c0,0-0.98,4.43,0.67,4.24c0,0,1.66,0.12,2.18-2.67l1.25-6.9l0.02-0.1
			c0.12-0.8,0.38-2.39,1.8-2.34c0,0,1.51,0,1.35,1.24c0,0-1.14,7.59-1.8,13.03c9.68-5.35,17.2-8.54,17.2-8.54
			c0.29-0.15,0.17-0.21,0.17-0.21c-1.16-0.39-0.61-2.96-0.61-2.96l0.57-5.19c0.02-1.28-1.55-0.52-1.55-0.52
			c-1.11,0.61-1.87,2.04-1.87,2.04c-0.65,0.89-1.04,5.43-1.04,5.43c-0.16,2.32-1.52,2.7-1.52,2.7c-1.61,0.56-1.79-0.34-1.79-0.34
			c-0.19-0.41,0.18-2.62,0.18-2.62l0.7-5.65c0.13-2.38,1.88-2.84,1.88-2.84c0.4-0.12,0.91-0.2,1.48-0.01
			c0.57,0.19,0.96,0.15,1.3,0.07c0.59-0.14,1.35-0.85,1.35-0.85c1.56-1.37,2.52-1.26,2.52-1.26c1.83-0.01,1.58,2.31,1.58,2.31
			l-0.64,6.38c-0.2,2.71,1.8,1.68,1.85,1.65L93.3,31.82z"/>
	</g>
</g>
</svg>
