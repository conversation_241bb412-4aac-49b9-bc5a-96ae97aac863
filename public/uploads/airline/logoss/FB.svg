<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 79.63 80.27" style="enable-background:new 0 0 79.63 80.27;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:url(#SVGID_00000044879427385333792260000005216554674776101809_);}
	.st2{fill:url(#SVGID_00000033334431464274556760000008363622654257137843_);}
	.st3{fill:url(#SVGID_00000027578895233894261630000006541299264667720589_);}
</style>
<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="21.8518" y1="46.2259" x2="-34.7866" y2="66.6154" gradientTransform="matrix(1 0.144 0.041 -1.013 38.4898 92.6505)">
	<stop  offset="0" style="stop-color:#E5E5E5"/>
	<stop  offset="0.119" style="stop-color:#EEEEEE"/>
	<stop  offset="0.264" style="stop-color:#FFFFFF"/>
	<stop  offset="0.455" style="stop-color:#FBFBFB"/>
	<stop  offset="0.635" style="stop-color:#F0F0F0"/>
	<stop  offset="0.81" style="stop-color:#DDDDDD"/>
	<stop  offset="0.981" style="stop-color:#C2C2C2"/>
	<stop  offset="1" style="stop-color:#BFBFBF"/>
</linearGradient>
<path class="st0" d="M78.79,51.37c-2.67-7.08-9.31-12.34-19.21-16.88C36.27,23.79,3.22,15.32,0,0
	c1.62,27.41,39.38,31.46,61.25,41.76c5.38,2.53,9.93,6.65,12.65,9.42c4.33,4.41,2.23,15.03-2.36,19.43
	C80.96,63.74,80.21,55.12,78.79,51.37z"/>
<linearGradient id="SVGID_00000098940337551817782670000015825254132577474726_" gradientUnits="userSpaceOnUse" x1="-34.8169" y1="57.7479" x2="-34.4511" y2="8.2653" gradientTransform="matrix(1.024 -0.207 -0.563 -0.918 98.0256 63.2938)">
	<stop  offset="0.093" style="stop-color:#7FBB85"/>
	<stop  offset="0.167" style="stop-color:#73B874"/>
	<stop  offset="0.338" style="stop-color:#5AAF53"/>
	<stop  offset="0.486" style="stop-color:#45A73D"/>
	<stop  offset="0.604" style="stop-color:#38A231"/>
	<stop  offset="0.676" style="stop-color:#33A02C"/>
	<stop  offset="0.885" style="stop-color:#00742F"/>
</linearGradient>
<path style="fill:url(#SVGID_00000098940337551817782670000015825254132577474726_);" d="M70.16,46.23
	C53.62,34.68,11.34,26.79,7.07,19.65c8.59,21.95,48.9,20.81,63.66,34.37c11.18,10.27-1.24,18.16-4.14,20
	C77.14,68.18,83.87,55.82,70.16,46.23z"/>
<linearGradient id="SVGID_00000148619878398564310590000003881237868523760049_" gradientUnits="userSpaceOnUse" x1="-27.8164" y1="60.4927" x2="-27.5861" y2="29.276" gradientTransform="matrix(1.048 4.000000e-03 -0.388 -0.956 92.1367 90.5419)">
	<stop  offset="0.066" style="stop-color:#E36853"/>
	<stop  offset="0.352" style="stop-color:#FF0000"/>
	<stop  offset="0.533" style="stop-color:#FD0002"/>
	<stop  offset="0.628" style="stop-color:#F40006"/>
	<stop  offset="0.702" style="stop-color:#E70009"/>
	<stop  offset="0.766" style="stop-color:#D3000C"/>
	<stop  offset="0.823" style="stop-color:#BA0010"/>
	<stop  offset="0.85" style="stop-color:#AB0012"/>
</linearGradient>
<path style="fill:url(#SVGID_00000148619878398564310590000003881237868523760049_);" d="M75.84,61.33
	C75.33,44,25.05,40.37,13.86,31.17c13.4,21.91,57.51,20.66,60.51,32.14c0.65,2.49-0.87,4.9-2.64,6.67
	C74.22,67.53,75.96,65.44,75.84,61.33z"/>
<linearGradient id="SVGID_00000005947506245368771720000003120394423532819627_" gradientUnits="userSpaceOnUse" x1="62.3704" y1="11.604" x2="74.5685" y2="33.8324" gradientTransform="matrix(1 0.029 0.029 -1 9.452754e-03 84.4677)">
	<stop  offset="0.066" style="stop-color:#E36853"/>
	<stop  offset="0.352" style="stop-color:#FF0000"/>
	<stop  offset="0.533" style="stop-color:#FD0002"/>
	<stop  offset="0.628" style="stop-color:#F40006"/>
	<stop  offset="0.702" style="stop-color:#E70009"/>
	<stop  offset="0.766" style="stop-color:#D3000C"/>
	<stop  offset="0.823" style="stop-color:#BA0010"/>
	<stop  offset="0.85" style="stop-color:#AB0012"/>
</linearGradient>
<path style="fill:url(#SVGID_00000005947506245368771720000003120394423532819627_);" d="M52.93,79.91
	C68.11,74.82,75.25,62.54,66.6,58.4C82.8,63.41,71.13,74.4,52.93,79.91z"/>
</svg>
