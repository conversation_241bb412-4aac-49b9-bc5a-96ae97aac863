<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 100.56 80" style="enable-background:new 0 0 100.56 80;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#009FE3;}
	.st1{fill:#F9B000;}
	.st2{fill:url(#SVGID_1_);}
	.st3{fill:#2BA937;}
	.st4{fill:url(#SVGID_00000101782260307310899060000011572065578096874647_);}
	.st5{fill:url(#SVGID_00000145021967253326340800000006336043880359480467_);}
	.st6{fill:url(#SVGID_00000049202929812968851100000011628908987400401835_);}
	.st7{fill:url(#SVGID_00000096056988736211740210000010080979367496006276_);}
	.st8{fill:url(#SVGID_00000117638018536842311850000006668194771450590900_);}
</style>
<g id="Layer_2">
</g>
<g id="Layer_1">
	<path class="st0" d="M9.91,62.91c0,0,29.23,4.21,40.12,17.09l5.94-4.21C55.98,75.54,37.15,54.49,9.91,62.91z"/>
	<path class="st1" d="M50.53,70.84c0,0,19.81-15.11,40.12-6.93c0,0-16.84-0.99-34.67,11.89C55.98,75.79,50.53,70.84,50.53,70.84z"/>
	
		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="67.0209" y1="51.8144" x2="76.8883" y2="48.2231" gradientTransform="matrix(1 0 0 -1 0 82.4)">
		<stop  offset="0.25" style="stop-color:#55B031"/>
		<stop  offset="1" style="stop-color:#C5D300"/>
	</linearGradient>
	<path class="st2" d="M57.21,57.21l20.31-53c0,0,1.98,25.51-2.97,35.17c0,0-2.23,5.7-9.66,10.15
		C65.14,49.54,60.43,52.26,57.21,57.21z"/>
	<path class="st3" d="M70.34,44.33l-10.9,7.18l0.5-0.99C59.94,50.53,70.34,44.33,70.34,44.33z M71.58,41.36l-10.9,7.18l0.5-0.99
		C61.18,47.31,68.11,42.85,71.58,41.36z M72.82,37.65l-10.9,7.18l0.5-0.99C62.41,43.84,72.82,37.65,72.82,37.65z M74.3,33.68
		l-10.9,7.43l0.5-0.99L74.3,33.68z M74.8,30.46l-10.15,7.93l0.5-0.99C64.89,37.4,69.6,33.68,74.8,30.46z M75.05,27.74l-9.16,7.18
		l0.5-0.99C66.38,33.93,75.05,27.74,75.05,27.74z M74.3,25.26l-6.93,5.94l0.5-0.99C67.86,30.22,70.59,27.74,74.3,25.26z
		 M75.54,22.04l-6.93,5.94L69.1,27C69.1,26.75,72.07,24.27,75.54,22.04z M75.54,18.33c0,0-4.21,3.72-5.2,5.45l0.5-0.99
		C70.84,22.54,72.57,20.56,75.54,18.33z M76.04,15.6l-4.46,4.46l0.5-0.99C72.07,19.07,72.82,18.08,76.04,15.6z"/>
	
		<linearGradient id="SVGID_00000119112014242503407290000006593192352222421377_" gradientUnits="userSpaceOnUse" x1="81.1521" y1="43.1881" x2="89.048" y2="36.268" gradientTransform="matrix(1 0 0 -1 0 82.4)">
		<stop  offset="0.25" style="stop-color:#55B031"/>
		<stop  offset="1" style="stop-color:#C5D300"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000119112014242503407290000006593192352222421377_);" d="M62.41,60.43l38.14-41.86
		c0,0-7.43,24.52-15.36,31.7c0,0-4.21,4.46-12.63,5.94C72.57,56.22,67.12,56.72,62.41,60.43z"/>
	<path class="st3" d="M79.26,53c0,0-11.15,1.73-12.63,2.72l0.74-0.74C67.37,54.98,79.26,53,79.26,53z M81.49,50.77
		c0,0-11.15,1.73-12.63,2.72l0.74-0.74C69.6,52.76,81.49,50.77,81.49,50.77z M84.21,47.8c0,0-11.15,1.73-12.63,2.72l0.74-0.74
		C72.32,49.78,84.21,47.8,84.21,47.8z M86.93,44.58c0,0-11.15,1.98-12.88,2.97l0.74-0.74C74.8,47.06,83.22,45.08,86.93,44.58z
		 M88.42,41.86l-12.38,3.72l0.74-0.74L88.42,41.86z M89.91,39.38l-11.15,3.47l0.74-0.74C79.5,41.86,85.7,40.12,89.91,39.38z
		 M89.91,36.9c0,0-6.93,1.98-8.67,2.97l0.74-0.74C81.98,39.13,85.7,37.65,89.91,36.9z M92.38,34.18c0,0-6.93,1.98-8.67,2.97
		l0.74-0.74C84.46,36.41,88.42,34.92,92.38,34.18z M93.62,30.71l-6.93,2.97l0.74-0.74C87.68,32.94,90.15,31.7,93.62,30.71z
		 M95.11,28.24c0,0-4.21,1.73-5.7,2.72l0.74-0.74C90.15,30.22,91.15,29.72,95.11,28.24z M77.52,11.64l-4.46,4.46l0.5-0.99
		C73.56,15.11,74.3,14.61,77.52,11.64z M77.03,9.16c0,0-0.99,1.49-2.23,2.97l0.5-0.99C75.29,10.9,76.28,9.91,77.03,9.16z"/>
	
		<linearGradient id="SVGID_00000081620523891677787010000001894255565655938230_" gradientUnits="userSpaceOnUse" x1="49.7759" y1="54.0311" x2="60.2775" y2="54.2342" gradientTransform="matrix(1 0 0 -1 0 82.4)">
		<stop  offset="0.25" style="stop-color:#55B031"/>
		<stop  offset="1" style="stop-color:#C5D300"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000081620523891677787010000001894255565655938230_);" d="M50.28,56.72V0c0,0,10.9,23.03,9.91,33.93
		c0,0-0.25,6.19-5.2,12.88C54.98,46.81,51.27,50.77,50.28,56.72z"/>
	<path class="st3" d="M57.71,39.88c0,0-6.93,8.67-7.43,10.65v-0.99C50.28,49.29,55.23,42.6,57.71,39.88z M57.71,36.66
		c0,0-6.93,8.67-7.43,10.65v-0.99C50.28,46.07,55.23,39.38,57.71,36.66z M57.71,32.69c0,0-6.93,8.67-7.43,10.65v-0.99
		C50.28,42.35,57.71,32.69,57.71,32.69z M57.71,28.73l-7.43,10.9v-0.99C50.28,38.39,55.23,31.21,57.71,28.73z M56.97,25.51
		c0,0-6.19,9.16-6.69,11.15v-0.99C50.28,35.42,53.5,30.22,56.97,25.51z M56.22,22.79l-5.94,9.91V31.7L56.22,22.79z M54.74,20.8
		l-4.46,7.93v-0.99C50.28,27.74,52.26,24.27,54.74,20.8z M54.74,17.09l-4.46,7.93v-0.99C50.28,24.02,52.26,20.56,54.74,17.09z
		 M53.25,13.87c0,0-2.48,4.95-2.97,6.93v-0.99C50.28,19.57,51.27,16.84,53.25,13.87z M52.76,10.9c0,0-1.98,3.96-2.48,5.94v-0.99
		C50.28,15.85,50.77,14.61,52.76,10.9z M52.76,6.69c0,0-1.98,3.96-2.48,5.94v-0.99C50.28,11.64,50.77,10.9,52.76,6.69z M51.27,4.71
		l-0.99,3.47V7.18C50.28,6.93,51.02,5.7,51.27,4.71z"/>
	
		<linearGradient id="SVGID_00000054968798935593393670000002519658117434309518_" gradientUnits="userSpaceOnUse" x1="33.6785" y1="51.7423" x2="23.811" y2="48.151" gradientTransform="matrix(1 0 0 -1 0 82.4)">
		<stop  offset="0.25" style="stop-color:#55B031"/>
		<stop  offset="1" style="stop-color:#C5D300"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000054968798935593393670000002519658117434309518_);" d="M43.34,57.21L23.03,4.46
		c0,0-1.98,25.51,2.97,35.17c0,0,2.23,5.7,9.66,10.15C35.42,49.54,40.37,52.26,43.34,57.21z"/>
	<path class="st3" d="M30.22,44.33l10.9,7.18l-0.5-0.99C40.62,50.53,33.68,45.82,30.22,44.33z M29.23,41.36l10.9,7.18l-0.5-1.24
		L29.23,41.36z M27.74,37.65l10.9,7.18l-0.5-0.99C38.14,43.84,27.74,37.65,27.74,37.65z M26.5,33.68l10.9,7.43l-0.5-0.99
		C36.66,40.12,29.72,35.42,26.5,33.68z M25.76,30.46c0,0,9.16,6.44,10.15,7.93l-0.5-0.99C35.67,37.4,30.96,33.68,25.76,30.46z
		 M25.51,27.74l9.16,7.18l-0.5-0.99C34.43,33.93,29.23,29.97,25.51,27.74z M26.5,25.26l6.93,5.94l-0.5-0.99
		C32.94,30.22,26.5,25.26,26.5,25.26z M25.02,22.04l6.93,5.94L31.46,27C31.7,26.75,28.48,24.27,25.02,22.04z M25.02,18.33
		c0,0,4.21,3.72,5.2,5.45l-0.5-0.99C29.97,22.54,28.24,20.56,25.02,18.33z M24.52,15.6l4.46,4.46l-0.5-0.99
		C28.73,19.07,27.74,18.08,24.52,15.6z"/>
	
		<linearGradient id="SVGID_00000012466377565564082160000012730592187036970141_" gradientUnits="userSpaceOnUse" x1="19.4739" y1="43.2482" x2="11.5779" y2="36.3281" gradientTransform="matrix(1 0 0 -1 0 82.4)">
		<stop  offset="0.25" style="stop-color:#55B031"/>
		<stop  offset="1" style="stop-color:#C5D300"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000012466377565564082160000012730592187036970141_);" d="M38.14,60.43L0,18.58
		c0,0,7.43,24.52,15.36,31.7c0,0,4.21,4.46,12.63,5.94C27.99,56.22,33.44,56.72,38.14,60.43z"/>
	<path class="st3" d="M21.3,53c0,0,11.15,1.73,12.63,2.72l-0.74-0.74C33.19,54.98,21.3,53,21.3,53z M19.07,50.77
		c0,0,11.15,1.73,12.63,2.72l-0.74-0.74C30.96,52.76,19.07,50.77,19.07,50.77z M16.59,47.8c0,0,11.15,1.73,12.63,2.72l-0.74-0.74
		C28.48,49.78,16.59,47.8,16.59,47.8z M13.87,44.58c0,0,11.15,1.98,12.88,2.97l-0.99-0.5C25.76,47.06,13.87,44.58,13.87,44.58z
		 M12.14,41.86l12.38,3.72l-0.74-0.74C23.78,44.83,12.14,41.86,12.14,41.86z M10.9,39.38l11.15,3.47l-0.74-0.74
		C21.3,41.86,15.11,40.12,10.9,39.38z M10.9,36.9c0,0,6.93,1.98,8.67,2.97l-0.74-0.74C18.58,39.13,15.11,37.65,10.9,36.9z
		 M8.42,34.18c0,0,6.93,1.98,8.67,2.97l-0.74-0.74C16.1,36.41,12.38,34.92,8.42,34.18z M6.93,30.71l6.93,2.97l-0.74-0.74
		C13.13,32.94,10.65,31.7,6.93,30.71z M5.45,28.24c0,0,4.21,1.73,5.7,2.72l-0.74-0.74C10.65,30.22,9.41,29.72,5.45,28.24z
		 M23.03,11.64l4.46,4.46L27,15.11C27,15.11,26.25,14.61,23.03,11.64z M23.78,9.16c0.7,1.03,1.44,2.02,2.23,2.97l-0.5-0.99
		C25.51,10.9,24.27,9.91,23.78,9.16z"/>
	
		<linearGradient id="SVGID_00000035508829684755042540000005154041157663781304_" gradientUnits="userSpaceOnUse" x1="50.9027" y1="54.0289" x2="40.4036" y2="54.2296" gradientTransform="matrix(1 0 0 -1 0 82.4)">
		<stop  offset="0.25" style="stop-color:#55B031"/>
		<stop  offset="1" style="stop-color:#C5D300"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000035508829684755042540000005154041157663781304_);" d="M50.28,56.72V0c0,0-10.9,23.03-9.91,33.93
		c0,0,0.25,6.19,5.2,12.88C45.82,46.81,49.29,50.77,50.28,56.72z"/>
	<path class="st3" d="M42.85,39.88c0,0,6.93,8.67,7.43,10.65v-0.99C50.28,49.29,45.33,42.6,42.85,39.88z M42.85,36.66
		c0,0,6.93,8.67,7.43,10.65v-0.99C50.28,46.07,45.33,39.38,42.85,36.66z M42.85,32.69c0,0,6.93,8.67,7.43,10.65v-0.99
		C50.28,42.35,42.85,32.69,42.85,32.69z M43.1,28.73l7.43,10.9v-0.99C50.28,38.39,45.57,31.21,43.1,28.73z M43.59,25.51
		c0,0,6.19,9.16,6.69,11.15v-0.99C50.28,35.42,47.31,30.22,43.59,25.51z M44.33,22.79l5.94,9.91V31.7
		C50.28,31.7,47.06,26.01,44.33,22.79z M46.07,20.8l4.46,7.93v-0.99C50.28,27.74,48.54,24.27,46.07,20.8z M46.07,17.09l4.46,7.93
		v-0.99C50.28,24.02,48.54,20.56,46.07,17.09z M47.31,13.87c0,0,2.48,4.95,2.97,6.93v-0.99C50.28,19.57,49.29,16.84,47.31,13.87z
		 M47.8,10.9c0,0,1.98,3.96,2.48,5.94v-0.99C50.28,15.85,50.03,14.61,47.8,10.9z M47.8,6.69c0,0,1.98,3.96,2.48,5.94v-0.99
		C50.28,11.64,49.78,10.9,47.8,6.69z M49.29,4.71l0.99,3.47V7.18C50.28,6.93,49.54,5.7,49.29,4.71z"/>
</g>
</svg>
