<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 220.61 80" style="enable-background:new 0 0 220.61 80;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#ED4138;}
	.st1{fill:#00693A;}
	.st2{fill:#DF1804;}
	.st3{fill:#214078;}
	.st4{fill:#3487C7;}
	.st5{fill:url(#SVGID_1_);}
	.st6{fill:url(#SVGID_2_);}
	.st7{fill:#184A8A;}
	.st8{fill:url(#SVGID_3_);}
	.st9{fill:url(#SVGID_4_);}
	.st10{fill:url(#SVGID_5_);}
	.st11{fill:url(#SVGID_6_);}
	.st12{fill:#9E8266;}
	.st13{fill:#322E26;}
	.st14{fill:#332F27;}
	.st15{fill:#791722;}
	.st16{fill:url(#SVGID_7_);}
	.st17{fill:url(#SVGID_8_);}
	.st18{fill:url(#SVGID_9_);}
	.st19{fill:url(#SVGID_10_);}
	.st20{fill:url(#SVGID_11_);}
	.st21{fill:url(#SVGID_12_);}
	.st22{fill:url(#SVGID_13_);}
	.st23{fill:url(#SVGID_14_);}
	.st24{fill:url(#SVGID_15_);}
	.st25{fill:url(#SVGID_16_);}
	.st26{fill:url(#SVGID_17_);}
	.st27{fill:url(#SVGID_18_);}
	.st28{fill:url(#SVGID_19_);}
	.st29{fill:url(#SVGID_20_);}
	.st30{fill:url(#SVGID_21_);}
	.st31{fill:#222325;}
	.st32{fill:#A0925A;}
	.st33{fill:#061E34;}
	.st34{fill:#028E8F;}
	.st35{fill:#D5192C;}
	.st36{fill:#FAC201;}
	.st37{fill:url(#SVGID_22_);}
	.st38{fill:url(#SVGID_23_);}
	.st39{fill:url(#SVGID_24_);}
	.st40{fill:url(#SVGID_25_);}
	.st41{fill:#9F8A58;}
	.st42{fill:#749ED4;}
	.st43{fill:#045991;}
	.st44{fill:#1F9A8D;}
	.st45{fill:#222D68;}
	.st46{fill:#14152E;}
	.st47{fill:#62B3E4;}
	.st48{fill:#FEFEFE;}
	.st49{fill:none;}
	.st50{fill:#FC0302;}
	.st51{fill:#FE0807;}
	.st52{fill:#98684C;}
	.st53{fill:#86754D;}
	.st54{fill:#42484D;}
	.st55{fill:#41474C;}
	.st56{fill:#43494D;}
	.st57{fill:#DF073D;}
	.st58{fill:#DE023A;}
	.st59{fill:#FFFFFF;}
	.st60{fill:#6F6E6C;}
	.st61{fill:#072F67;}
	.st62{fill:#53A220;}
	.st63{fill:#FDC013;}
	.st64{fill:url(#SVGID_26_);}
	.st65{fill:url(#SVGID_27_);}
	.st66{fill:url(#SVGID_28_);}
	.st67{fill:#793A98;}
	.st68{fill:url(#SVGID_29_);}
	.st69{fill:url(#SVGID_30_);}
	.st70{fill:url(#SVGID_31_);}
	.st71{fill:url(#SVGID_32_);}
	.st72{fill:url(#SVGID_33_);}
</style>
<g>
	<path class="st63" d="M0.62,44.1c3.06-1.44,6.13-2.76,9.5-3.42c4.95-0.97,9.8-0.52,14.58,0.74c5.8,1.53,11,4.26,15.45,8.35
		c1.33,2,5.85,5,6.21,5.11c6.58,1.93,13.18,3.4,20.13,2.37c4.15-0.61,8.15-1.65,11.9-3.52c1.08-0.54,2.01-1.37,3.21-1.68
		c2.02,0.27,3.94-0.45,5.89-0.71c7.28-0.99,14.3-0.11,21.19,2.23c6.12,2.07,11.7,5.24,17.26,8.48c2.93,1.7,5.75,3.61,8.91,4.9
		c0.1,0.32,2.74,1.57,2.97,1.68c8.22,3.99,16.79,6.49,26.03,6.27c-2.49,1.27-5.27,1.21-7.92,1.49c-4.44,0.47-8.96,0.49-13.41,0.06
		c-6.99-0.69-13.77-2.35-20.28-5.08c-5.18-2.17-10.12-4.79-14.62-8.13c-2.49-1.85-4.99-1.94-7.75-1.15
		c-5.84,1.67-10.93,4.56-15.47,8.63c-3.76,3.37-8.19,5.76-13.06,7.27c-7,2.18-13.81,1.56-20.48-1.3c-1.03-0.44-2.07-0.87-3.09-1.35
		c-3.31-1.54-6.1-3.73-8.65-6.36c-5.56-5.72-11.37-11.19-17.74-16.02c-5.52-4.19-11.58-7.16-18.48-8.24
		c-0.74-0.12-1.51-0.03-2.27-0.04C0.62,44.48,0.62,44.29,0.62,44.1z M76.06,61.66c-0.55,0.12-0.71,0.07-1.65,0.36
		c-2.33,0.73-4.67,1.43-7.15,1.48c-4.46,0.09-8.46-1.57-12.49-3.15c-1.01-0.4-1.9-1.27-3.27-0.61c3.4,2.65,6.76,5.2,10.57,7.11
		c5.1,2.56,9.94,2.38,14.59-1.02c0.98-0.11,1.74-0.72,2.57-1.15c4.63-2.45,9.51-4.05,14.73-4.79c1.85-0.26,3.98-0.15,5.53-1.66
		c-0.47-0.83-1.23-0.87-1.98-0.93c-4.05-0.32-8.14-0.47-12.06,0.68C82.24,58.93,79.54,59.96,76.06,61.66z"/>
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="39.731" y1="41.422" x2="135.3384" y2="41.422">
		<stop  offset="0" style="stop-color:#10B48D"/>
		<stop  offset="0.0196" style="stop-color:#11B591"/>
		<stop  offset="0.1973" style="stop-color:#19BAB1"/>
		<stop  offset="0.3812" style="stop-color:#1FBECB"/>
		<stop  offset="0.5713" style="stop-color:#24C1DD"/>
		<stop  offset="0.7719" style="stop-color:#26C2E8"/>
		<stop  offset="1" style="stop-color:#27C3EB"/>
	</linearGradient>
	<path class="st5" d="M81.59,52.05c-1.2,0.32-2.13,1.14-3.21,1.68c-3.75,1.87-7.75,2.91-11.9,3.52c-6.95,1.02-13.56-0.45-20.13-2.37
		c-0.36-0.11-5.14-3.25-6.62-5.53c1.03,0.08,2.39,0.58,3.45,0.91c7.15,2.22,12.87,1.06,19.13-1.3c6.08-2.29,11.41-5.9,16.86-9.3
		c4.33-2.7,8.46-5.73,13-8.09c2.52-1.3,5.02-2.68,7.62-3.77c5.63-2.36,12.05-2.34,12.5-2.38c8.12-0.72,15.74,0.93,23.05,4.68
		c-0.54,0.44-1.01,0.32-1.45,0.32c-14.76-0.26-27.98,4.34-40.13,12.55c-1.65,1.11-3.04,2.36-4.64,3.54
		c-0.35,0.26-1.58,1.18-1.73,1.28c-0.88,0.61-1.91,1.22-2.4,1.55C83.8,50.15,82.43,50.77,81.59,52.05z"/>
	<g>
		<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="203.734" y1="37.2893" x2="142.4138" y2="39.7193">
			<stop  offset="0" style="stop-color:#E3068D"/>
			<stop  offset="0.2891" style="stop-color:#D30D8D"/>
			<stop  offset="0.8552" style="stop-color:#A81E8F"/>
			<stop  offset="1" style="stop-color:#9C238F"/>
		</linearGradient>
		<path class="st6" d="M220.23,0.59c-0.84-0.07-1.68,0.54-2.29,0.98c-5.01,3.6-8.21,8.42-10.36,14.18
			c-3.06,8.16-6.96,15.93-12.64,22.68c-3.81,4.53-8.44,7.81-14.2,9.42c-0.84,0.24-0.86,0.29-2.19,0.63
			c-0.5,0.13-1.01,0.21-1.53,0.23c-5.17,0.22-10.31-0.28-15.35-1.32c-6.74-1.39-13.39-3.22-20.13-4.63
			c-4.99-1.04-7.54-1.47-15.13-2.14c-1.06-0.04-1.17-0.12-2.56-0.18c-4.62-0.2-9.23-0.55-13.85,0.15
			c-7.15,1.08-19.88,6.32-22.29,7.42c-1.21,0.57-2.17,0.99-2.7,1.35c-1.2,0.81-2.57,1.43-3.4,2.7c2.02,0.27,3.94-0.45,5.89-0.71
			c7.28-0.99,14.3-0.11,21.19,2.23c6.12,2.07,11.7,5.24,17.26,8.48c2.93,1.7,5.29,3.29,8.8,5.1c2.22,1.08,2.85,1.37,3.09,1.48
			c8.22,3.99,16.79,6.49,26.03,6.27c1.53,0.1,2.99-0.35,4.48-0.59c8.89-1.49,17.4-4.12,25.26-8.6c2.52-1.44,7.68-5.16,7.88-5.33
			c5.83-4.97,10.62-10.75,14.08-17.62c0.6-1.19,0.5-1.98-0.21-3.09c-1.75-2.74-1.72-5.54,0.35-8.16c0.48-0.61,0.93-1.14,0.9-2
			c-0.11-2.65-0.03-5.3-0.27-7.94c-0.37-4.06-0.2-8.13-0.28-12.19c-0.01-0.54,0.07-1.16,0.14-1.72
			C216.63,4.2,218.42,2.43,220.23,0.59"/>
	</g>
	<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="81.5711" y1="57.559" x2="201.4269" y2="57.559">
		<stop  offset="0" style="stop-color:#E1038C"/>
		<stop  offset="0.6466" style="stop-color:#B7178D"/>
		<stop  offset="1" style="stop-color:#A51F8E"/>
	</linearGradient>
	<path class="st8" d="M126.38,40.63c-1.06-0.04-1.17-0.12-2.56-0.18c-4.62-0.2-9.23-0.55-13.85,0.15
		c-7.15,1.08-19.88,6.32-22.29,7.42c-1.21,0.57-2.17,0.99-2.7,1.35c-1.2,0.81-2.57,1.43-3.4,2.7c2.02,0.27,3.94-0.45,5.89-0.71
		c7.28-0.99,14.3-0.11,21.19,2.23c6.12,2.07,11.7,5.24,17.26,8.48c2.93,1.7,5.29,3.29,8.8,5.1c2.22,1.08,2.85,1.37,3.09,1.48
		c8.22,3.99,16.79,6.49,26.03,6.27c1.53,0.1,2.99-0.35,4.48-0.59c8.89-1.49,17.4-4.12,25.26-8.6c2.52-1.44,7.68-5.16,7.88-5.33
		C201.43,60.39,153.41,67.22,126.38,40.63z"/>
	<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="47.7854" y1="62.5474" x2="101.5047" y2="62.5474">
		<stop  offset="0" style="stop-color:#A51F8E"/>
		<stop  offset="1" style="stop-color:#E1038C"/>
	</linearGradient>
	<path class="st9" d="M97.52,57.29c-4.05-0.32-8.14-0.47-12.06,0.68c-3.15,0.93-2.81,0.4-9.01,3.34c-0.24,0.12-0.51,0.22-0.77,0.3
		c-0.99,0.31-0.19,0.08-1.42,0.46c-2.33,0.73-4.51,1.23-7,1.28c-4.46,0.09-8.55-1.24-12.5-3.01c-2.72-1.22-5.19-3.47-6.98-3.93
		c3.2,3.57,10.47,8.51,14.28,10.42c5.1,2.56,9.59,2.52,14.6-0.65c2.15-1.21,1.63-1.03,2.57-1.53c4.63-2.45,9.51-4.05,14.73-4.79
		c1.85-0.26,5.03-0.65,7.54-1.23C99.83,57.84,98.27,57.35,97.52,57.29z"/>
</g>
</svg>
