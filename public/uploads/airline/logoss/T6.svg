<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 39.2 80" style="enable-background:new 0 0 39.2 80;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:#0097CE;}
	.st2{fill:url(#SVGID_00000072263207451931155760000005563863908749417611_);}
</style>
<g>
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="24.9827" y1="79.7253" x2="24.9827" y2="42.0921">
		<stop  offset="0" style="stop-color:#006799"/>
		<stop  offset="0.9997" style="stop-color:#27365A"/>
	</linearGradient>
	<path class="st0" d="M34.2,55.5c0,0-1.5,4.4-2.3,12.6C31.2,76.3,29.2,80,29.2,80s-3.8-5.8-5.2-10.2c-1.5-4.4,1.4-9.4,1.4-17.7
		c0-8.4-9.4-10.7-9.6-10.7c2,0.2,4,0.7,6.1,1.6C27.7,45.5,33.7,53.1,34.2,55.5C34.2,55.5,34.2,55.5,34.2,55.5z"/>
	<path class="st1" d="M36.2,38.5c-0.6-1.7-5.3-1.7-5.3-1.7c5.3-4.1,3.3-12.3,3.3-12.3s0,0,0,0c-0.6,2.4-6.5,10-12.4,12.5
		c-2.1,0.9-4.2,1.4-6.1,1.6C12.3,39,9,38.3,5.6,37.4C0.4,35.9,0,36.7,0,36.7c0.8,1.8,3.9,2.1,4.9,2.4c1,0.3,1.2,0.9,1.2,0.9
		s-0.1,0.7-1.2,0.9c-1,0.3-4.1,0.6-4.9,2.4c0,0,0.4,0.8,5.6-0.7c3.4-0.9,6.6-1.6,10.1-1.3c2,0.2,4,0.7,6.1,1.6
		c5.9,2.6,11.8,10.2,12.4,12.5c0,0,0,0,0,0s2-8.2-3.3-12.3c0,0,4.7,0,5.3-1.7c0,0,2.9-0.1,2.9-1.5S36.2,38.5,36.2,38.5z"/>
	
		<linearGradient id="SVGID_00000166654486876427131820000010123714564497380753_" gradientUnits="userSpaceOnUse" x1="24.9827" y1="-6.1146" x2="24.9827" y2="36.9885">
		<stop  offset="0" style="stop-color:#006799"/>
		<stop  offset="0.9997" style="stop-color:#27365A"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000166654486876427131820000010123714564497380753_);" d="M34.2,24.5c0,0-1.5-4.4-2.3-12.6
		C31.2,3.7,29.2,0,29.2,0s-3.8,5.8-5.2,10.2c-1.5,4.4,1.4,9.4,1.4,17.7c0,8.4-9.4,10.7-9.6,10.7c2-0.2,4-0.7,6.1-1.6
		C27.7,34.5,33.7,26.9,34.2,24.5C34.2,24.5,34.2,24.5,34.2,24.5z"/>
</g>
</svg>
