version: '3.7'
services:
  hyperf:
    build:
      context: .
    image: yshk_system_admin_api
    container_name: yshk_system_admin_api
    environment:
      - "APP_PROJECT=hyperf"
      - "APP_ENV=production"
    network_mode: host
    restart: always
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 5
      update_config:
        parallelism: 2
        delay: 5s
        order: start-first
