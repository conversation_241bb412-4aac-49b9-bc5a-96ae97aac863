<?php

declare(strict_types=1);

/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

use App\Controller\Flight\FlightController;
use Hyperf\HttpServer\Router\Router;

//Router::get('/', [\App\Controller\IndexController::class, 'index']);

Router::get('/test', [\App\Controller\TestController::class, 'test']);

Router::post('/indexs', [\App\Controller\TestController::class, 'index']);

Router::post('/upload', [\App\Controller\TestController::class, 'upload']);

//运维校验程序是否正常使用，请勿修改
Router::get('/monitor', [\App\Controller\MonitorController::class, 'index']);

Router::get('/vy_test', [\App\Controller\TestController::class, 'vy_test']);
// 下载
Router::addGroup('/download', function () {
    Router::get('/down_excel', [\App\Controller\Download\DownloadController::class, 'downExcel']);           //下载
});

Router::get('/ping', [\App\Controller\PingController::class, 'ping']);

//开放(回调)
Router::addGroup('/notify', function () {

    Router::get('/notify/zf', [\App\Controller\NotifyController::class, 'zf']);
});

//胜意国内机票接口
Router::addGroup('/v2/{lang:en|zh_CN}', function () {
    Router::post('/flight/service', [\App\Controller\Flight\FlightV2Controller::class, 'service']);     //查询服务
}, ['middleware' => [\App\Middleware\ShengYiMiddleware::class]]);

Router::post('/v2/{lang:en|zh_CN}/flight/order/info', [\App\Controller\FlightOrder\FlightOrderV2Controller::class, 'info']);     //查询订单详情
Router::post('/v2/{lang:en|zh_CN}/flight/order/cancel', [\App\Controller\FlightOrder\FlightOrderV2Controller::class, 'cancel']); //取消订单
Router::post('/trip/list', [\App\Controller\Flight\FlightV2Controller::class, 'trip']);             //查询携程航班数据
Router::post('/v2/{lang:en|zh_CN}/flight/refund/cancel', [\App\Controller\Flight\FlightV2Controller::class, 'cancelRefund']); //退款订单取消

//胜意国际机票接口
Router::addGroup('/sy/overseas/{lang:en|zh_CN}', function () {
    Router::post('/flight/service', [\App\Controller\Flight\FlightV2Controller::class, 'serviceInter']);
}, ['middleware' => [\App\Middleware\ShengYiMiddleware::class]]);

//标准文档输出接口
Router::addGroup('/api/{lang:en|zh_CN|zh_TW}', function () {
	//机场数据查询
	Router::post('/airports/list', [\App\Controller\Flight\FlightAirportsController::class, 'list']);
	Router::post('/airports/search', [\App\Controller\Flight\FlightAirportsController::class, 'search']);
	//航司数据查询
	Router::post('/airlines/list', [\App\Controller\Air\AirlineController::class,'getList']);
	/******************国内接口*********************/
	//查询航班接口
	Router::post('/flight/search', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'flightSearch']);
	//获取更多仓位
	Router::post('/flight/cabin', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'getMoreCabin']);
	//验价
	Router::post('/flight/verify', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'verify']);
	//生成预订单
	Router::post('/flight/order/create', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'create']);
	//订单支付
	Router::post('/flight/order/pay', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'pay']);
	//订单详情
	Router::post('/flight/order/detail', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'orderDetail']);
	//订单取消
	Router::post('/flight/order/cancel', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'cancel']);
	//改签航班查询
	Router::post('/flight/change/search', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'changeSearch']);
	//提交改签申请
	Router::post('/flight/change/apply', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'changeFlightApply']);
	//改签订单详情
	Router::post('/flight/change/detail', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'changeDetail']);
	//改签订单取消
	Router::post('/flight/change/cancel', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'cancelChange']);
	//提交退票申请
	Router::post('/flight/refund/apply', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'refundApply']);
	//退票订单详情
	Router::post('/flight/refund/detail', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'refundDetail']);
	//退票订单取消
	Router::post('/flight/refund/cancel', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'cancelRefund']);
	//退票确认
	Router::post('/flight/refund/confirm', [\App\Controller\FlightDomestic\FlightDomesticController::class, 'refundConfirm']);
}, ['middleware' => [\App\Middleware\OpenApiMiddleware::class]]);

Router::addGroup('/api/overseas/{lang:en|zh_CN|zh_TW}', function () {
	/******************国际接口*********************/
	//查询航班接口
	Router::post('/flight/search', [\App\Controller\FlightInternational\FlightInternationalController::class, 'flightSearch']);
	//获取更多仓位
	Router::post('/flight/cabin', [\App\Controller\FlightInternational\FlightInternationalController::class, 'getMoreCabin']);
	//验价
	Router::post('/flight/verify', [\App\Controller\FlightInternational\FlightInternationalController::class, 'verify']);
	//生成预订单
	Router::post('/flight/order/create', [\App\Controller\FlightInternational\FlightInternationalController::class, 'create']);
	//订单支付
	Router::post('/flight/order/pay', [\App\Controller\FlightInternational\FlightInternationalController::class, 'pay']);
	//订单详情
	Router::post('/flight/order/detail', [\App\Controller\FlightInternational\FlightInternationalController::class, 'orderDetail']);
	//订单取消
	Router::post('/flight/order/cancel', [\App\Controller\FlightInternational\FlightInternationalController::class, 'cancel']);
	//改签航班查询
	Router::post('/flight/change/search', [\App\Controller\FlightInternational\FlightInternationalController::class, 'changeSearch']);
	//提交改签申请
	Router::post('/flight/change/apply', [\App\Controller\FlightInternational\FlightInternationalController::class, 'changeFlightApply']);
	//改签订单详情
	Router::post('/flight/change/detail', [\App\Controller\FlightInternational\FlightInternationalController::class, 'changeDetail']);
	//改签订单取消
	Router::post('/flight/change/cancel', [\App\Controller\FlightInternational\FlightInternationalController::class, 'cancelChange']);
	//提交退票申请
	Router::post('/flight/refund/apply', [\App\Controller\FlightInternational\FlightInternationalController::class, 'refundApply']);
	//退票订单详情
	Router::post('/flight/refund/detail', [\App\Controller\FlightInternational\FlightInternationalController::class, 'refundDetail']);
	//退票订单取消
	Router::post('/flight/refund/cancel', [\App\Controller\FlightInternational\FlightInternationalController::class, 'cancelRefund']);
	//退票确认
	Router::post('/flight/refund/confirm', [\App\Controller\FlightInternational\FlightInternationalController::class, 'refundConfirm']);
}, ['middleware' => [\App\Middleware\OpenApiMiddleware::class]]);

//Accomy版本整合
Router::addGroup('/api/v2/{lang:en|zh_CN|zh_TW}', function () {
    //查询航班接口
    Router::post('/flight/search', [FlightController::class, 'flightSearch']);
    //获取更多仓位
    Router::post('/flight/cabin', [FlightController::class, 'getMoreCabin']);
    //验价
    Router::post('/flight/verify', [FlightController::class, 'verify']);
    //生成预订单
    Router::post('/flight/order/create', [FlightController::class, 'create']);
    //订单支付
    Router::post('/flight/order/pay', [FlightController::class, 'pay']);
    //订单详情
    Router::post('/flight/order/detail', [FlightController::class, 'orderDetail']);
    //订单取消
    Router::post('/flight/order/cancel', [FlightController::class, 'cancel']);
    //改签航班查询
    Router::post('/flight/change/search', [FlightController::class, 'changeSearch']);
    //提交改签申请
    Router::post('/flight/change/apply', [FlightController::class, 'changeFlightApply']);
    //改签订单详情
    Router::post('/flight/change/detail', [FlightController::class, 'changeDetail']);
    //改签订单取消
    Router::post('/flight/change/cancel', [FlightController::class, 'cancelChange']);
    //提交退票申请
    Router::post('/flight/refund/apply', [FlightController::class, 'refundApply']);
    //退票订单详情
    Router::post('/flight/refund/detail', [FlightController::class, 'refundDetail']);
    //退票订单取消
    Router::post('/flight/refund/cancel', [FlightController::class, 'cancelRefund']);
    //退票确认
    Router::post('/flight/refund/confirm', [FlightController::class, 'refundConfirm']);
}, ['middleware' => [\App\Middleware\OpenApiMiddleware::class]]);


//供应商回调通知
Router::addGroup('/zf', function () {
    // 智飞出票通知
    Router::post("/ticket_notify", [\App\Controller\IssueTicket\IssueTicketController::class, "ZfTicketNotify"]);

    // 智飞航变通知接口
    Router::post("/flight_change_notify", [\App\Controller\IssueTicket\IssueTicketController::class, "ZfFlightChangeNotify"]);
});

Router::addGroup('/sy', function () {
    // 胜意出票通知
    Router::post("/notify", [\App\Controller\IssueTicket\IssueTicketController::class, "SyTicketNotify"]);
});


/**
 * ================================ 管理后台 start ======================================
 */
// 登录
Router::post('/login', [\App\Controller\UserController::class, 'login'], ['middleware' => [\App\Middleware\SystemLogMiddleware::class]]);
Router::get('/manage/purchase_platform/quota/query', [\App\Controller\SysPurchasePlatform::class, 'quotaQuery']);

Router::addGroup('/manage', function () {
    require BASE_PATH . '/routes/manage.php';
}, ['middleware' => [App\Middleware\JWTAuthMiddleware::class, App\Middleware\PermissionMiddleware::class, \App\Middleware\SystemLogMiddleware::class]]);


//开放ddc接口
Router::addGroup('/ddc', function () {
    // 获取国际航司单条
    Router::get('/air_airline/find', [\App\Controller\CommonController::class, 'getAirAirlineFind']);
    // 获取国际航司多条
    Router::get('/air_airline/list', [\App\Controller\CommonController::class, 'getAirAirlineList']);
    // 获取机场单条
    Router::get('/air_airports/find', [\App\Controller\CommonController::class, 'getAirAirportsFind']);
    // 获取机场多条
    Router::get('/air_airports/list', [\App\Controller\CommonController::class, 'getAirAirportsList']);
    // 获取单条店铺信息
    Router::get('/shop/find', [\App\Controller\CommonController::class, 'getshopDetails']);
    Router::get('/cabin_code', [\App\Controller\Policy\PolicyController::class, 'cabinCode']);//适用仓位
});

//开放API
Router::addGroup('/open_api', function () {
    // 供应商开发接口
    Router::post("/supplier/notify/order", [\App\Controller\Supplier\NotifyController::class, "order"]);
    Router::post("/supplier/notify/cancel", [\App\Controller\Supplier\NotifyController::class, "cancel"]);
    // 店铺数据
    Router::get("/shop", [\App\Controller\ShopController::class, "getAllData"]);
    //降舱出票
    Router::addGroup('/downgrade_ticket', function () {
        //开始扫描
        Router::get('/start_scanning', [\App\Controller\DowngradeTicket\DowngradeTicketController::class, 'startScanning']);
    });

    //出票后降舱
    Router::addGroup('/downgrade_after_ticket', function () {
        //开始扫描
        Router::get('/start_scanning', [\App\Controller\DowngradeAfterTicket\DowngradeAfterTicketController::class, 'startScanning']);
    });
    // 定时拉取ETERM账单
    Router::get('/eterm/pull', [\App\Controller\Bill\PullEtermBillController::class, 'crontabPullEtermBill']);
    Router::addGroup("/op", function () {
        // 航班管家出票通知
        Router::post("/fly_steward/ticket_notify", [\App\Controller\IssueTicket\IssueTicketController::class, "fyStewardTicketNotify"]);
        // 易旅行支付通知
        Router::post("/e_travel/pay_notify", [\App\Controller\IssueTicket\IssueTicketController::class, "eTravelPayNotify"]);
        // 易旅行出票通知
        Router::post("/e_travel/ticket_notify", [\App\Controller\IssueTicket\IssueTicketController::class, "eTravelTicketNotify"]);
        //易旅行新订单通知
        Router::post("/e_travel/order_notify", [\App\Controller\IssueTicket\IssueTicketController::class, "ETravelOrderNotify"]);

    });
    // 发送短信
    Router::post("/sms", [App\Controller\OpenApi\SmsController::class, "send"]);
    // 易宝VCC开卡
    Router::post("/openVccCard", [\App\Controller\OpenApi\YiBaoVccCardController::class, "openCardByThreePlatform"]);
    // 易宝VCC取消卡
    Router::post("/cancelVccCard", [\App\Controller\OpenApi\YiBaoVccCardController::class, "cancelCardByThreePlatform"]);
    // 添加国内订单日志
    Router::post("/addOrderLog", [\App\Controller\OpenApi\DomesticOrderLogsController::class, "addOrderLog"]);
    // 添加国内订单签注信息
    Router::post("/addOrderRemark", [\App\Controller\OpenApi\DomesticOrderLogsController::class, "addOrderRemark"]);
    // 标记VCC卡为退票状态
    Router::post("/vccCardRefund", [\App\Controller\OpenApi\YiBaoVccCardController::class, "refund"]);
    // CDS通知
    Router::addGroup("/cds", function () {
        Router::post("/ctrip/payNotify", [\App\Controller\CheapAirline\CDSController::class, "payNotify"]);
    });
    Router::addGroup("/vj", function () {
        // 询价接口
        Router::post("/queryFareQuotes", [\App\Controller\CheapAirline\VjController::class, "queryFareQuotes"]);
        // 验价接口
        Router::post("/queryFlightInfo", [\App\Controller\CheapAirline\VjController::class, "queryFlightInfo"]);
        // 生单接口
        Router::post("/createOrder", [\App\Controller\CheapAirline\VjController::class, "reservedSeats"]);
        // 支付接口
        Router::post("/paymentTicket", [\App\Controller\CheapAirline\VjController::class, "paymentTicket"]);
        // 票号明细接口
        Router::post("/backFill", [\App\Controller\CheapAirline\VjController::class, "getTicketDetails"]);
        // 获取vj城市对
        Router::post("/getCityPairs", [\App\Controller\CheapAirline\VjController::class, "GetCityPairs"]);
    });
    # VY航司
    Router::addGroup("/vy", function () {
        // 询价接口
        Router::post("/queryFareQuotes", [\App\Controller\CheapAirline\VYController::class, "queryFareQuotes"]);
        // 验价接口
        Router::post("/queryFlightInfo", [\App\Controller\CheapAirline\VYController::class, "queryFlightInfo"]);
        // 生单接口
        Router::post("/createOrder", [\App\Controller\CheapAirline\VYController::class, "reservedFalse"]);
        Router::post("/createOrderTrue", [\App\Controller\CheapAirline\VYController::class, "reservedSeats"]);
        // 支付接口
        Router::post("/paymentTicket", [\App\Controller\CheapAirline\VYController::class, "paymentTicket"]);
        // 票号明细回填接口
        Router::post("/backFill", [\App\Controller\CheapAirline\VYController::class, "getTicketDetails"]);
    });
    # GDS-TravelPort
    Router::addGroup("/travelport", function () {
        // 询价接口
        Router::post("/queryFareQuotes", [\App\Controller\CheapAirline\TravelPortController::class, "queryFareQuotes"]);
        // 验价接口
        Router::post("/queryFlightInfo", [\App\Controller\CheapAirline\TravelPortController::class, "queryFlightInfo"]);
        // 生单接口
        Router::post("/createOrder", [\App\Controller\CheapAirline\TravelPortController::class, "reservedSeats"]);
        // 支付接口
        Router::post("/paymentTicket", [\App\Controller\CheapAirline\TravelPortController::class, "paymentTicket"]);
    });
    // 携程通知相关API
    Router::addGroup("/ctrip", function () {
        // 取消订单通知
        Router::post("/cancelOrderNotify", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "ctripCancelOrderNotify"]);
        // 催单通知
        Router::post("/urgeOrderNotify", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "ctripUrgeOrderNotify"]);
        // 携程验票通知
        Router::post("/checkTicketNotify", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "checkTicketNotify"]);
    });
    // 同程通知相关API
    Router::addGroup("/same", function () {
        // 订单通知
        Router::post("/orderNotify", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "sameOrderNotify"]);
    });
    // 寰宇通知相关API
    Router::addGroup("/huanyu", function () {
        // 寰宇出票通知
        Router::post("/ticketNotify", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "HuanyuTicketNotify"]);
    });
    // 订单分发通知
    Router::post("/orderInterval", [\App\Controller\OpenApi\OrderIntervalController::class, "createOrder"]);
    // 8000yi 待出票订单通知
    Router::post("/8000yi/{storeId}", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "eightThousand"]);
    // 国际分销取消订单通知
    Router::post("/distribution/cancelNotify/{storeId}", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "distributionCancelNotify"]);
    // 无锡提供 账单查询
    Router::get('/wux/reckoning/normal', [\App\Controller\Bill\ReckoningController::class, 'getNormalPageList']);   //正常单账单 列表
    Router::get('/wux/reckoning/change', [\App\Controller\Bill\ReckoningController::class, 'getChangePageList']);   //改签单账单 列表
    Router::get('/wux/reckoning/refund', [\App\Controller\Bill\ReckoningController::class, 'getRefundPageList']);   //退票单账单 列表
    Router::post('/wux/export/normal', [\App\Controller\Bill\ReckoningController::class, 'exportNormal']);          //导出 正常账单
    Router::post('/wux/export/change', [\App\Controller\Bill\ReckoningController::class, 'exportChange']);          //导出 改签账单
    Router::post('/wux/export/refund', [\App\Controller\Bill\ReckoningController::class, 'exportRefund']);          //导出 退票账单
    // 无锡提供 收支科目选择列表
    Router::get('/wux/subject/select', [\App\Controller\CommonController::class, 'subjectSelect']);
    Router::get('/wux/download/list', [\App\Controller\Download\DownloadController::class, 'list']);           //展示


});
// 航路 待出票订单通知
Router::post("/openApi/airRoute/{storeId}", [\App\Controller\OtaOrderNotify\OtaOrderNotify::class, "airRouteIssueTicketNotify"]);
// PNR的OTA支付状态
Router::post("/openApi/pnrOtaPayStatus", [\App\Controller\OpenApi\InterOrderPnrStatusController::class, "pnrOtaPayStatus"]);
Router::get('/get/field', [\App\Controller\SystemConfigController::class, 'getFieldRecord'], ['middleware' => [App\Middleware\JWTAuthMiddleware::class]]);
Router::post('/set/field', [\App\Controller\SystemConfigController::class, 'setFieldRecord'], ['middleware' => [App\Middleware\JWTAuthMiddleware::class]]);

Router::get("/vcc/query", [\App\Controller\YiBaoVccCardController::class, "queryCard"]);
Router::post("/callback/yinzhitravel/order", [\App\Controller\YinzhiTravelController::class, "acceptOrder"]);
// 导出NK待出票订单
Router::get("/exportNk", [\App\Controller\OpenApi\Export::class, "exportNkWaitTicket"]);

\Hyperf\SocketIOServer\Collector\SocketIORouter::addNamespace('/booking', \App\Controller\SocketIo\WebSocketController::class);

